<?php

declare(strict_types=1);

namespace App\Actions;

use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;

final class SendPasswordResetLink
{
    /**
     * Handle a password reset link request.
     */
    public function handle(array $data, ?string $broker = null): string
    {
        $status = Password::broker($broker)->sendResetLink($data);

        if ($status !== Password::RESET_LINK_SENT) {
            throw ValidationException::withMessages([
                'email' => [__($status)],
            ]);
        }

        return $status;
    }
}
