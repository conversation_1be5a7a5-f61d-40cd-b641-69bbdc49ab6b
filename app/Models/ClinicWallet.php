<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

final class ClinicWallet extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = [];

    /**
     * Get the clinic that owns the wallet.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the wallet definition for this wallet.
     */
    public function walletDefinition(): BelongsTo
    {
        return $this->belongsTo(WalletDefinition::class);
    }

    /**
     * Get the transactions for this wallet.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Get the approved transactions for this wallet.
     */
    public function approvedTransactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class)->where('status', 'approved');
    }

    /**
     * Get the balance in dollars.
     */
    public function getBalanceInDollarsAttribute(): float
    {
        return $this->balance / 100;
    }

    /**
     * Add amount to wallet balance.
     */
    public function addBalance(int $amount): void
    {
        $this->increment('balance', $amount);
    }

    /**
     * Subtract amount from wallet balance.
     */
    public function subtractBalance(int $amount): void
    {
        $this->decrement('balance', $amount);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'balance' => 'integer',
        ];
    }
}
