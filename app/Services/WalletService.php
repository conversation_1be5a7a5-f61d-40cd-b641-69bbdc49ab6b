<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Clinic;
use App\Models\ClinicWallet;
use App\Models\WalletDefinition;
use App\Models\WalletTransaction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

final class WalletService
{
    /**
     * Get or create a wallet for a clinic and wallet definition.
     */
    public function getOrCreateWallet(Clinic $clinic, WalletDefinition $walletDefinition): ClinicWallet
    {
        return ClinicWallet::firstOrCreate(
            [
                'clinic_id' => $clinic->id,
                'wallet_definition_id' => $walletDefinition->id,
            ],
            [
                'balance' => 0,
            ]
        );
    }

    /**
     * Record a transaction and update wallet balance.
     */
    public function recordTransaction(
        ClinicWallet $wallet,
        int $amount,
        string $type,
        string $description,
        ?string $sourceType = null,
        ?string $sourceId = null,
        ?array $metadata = null,
        string $status = 'approved'
    ): WalletTransaction {
        return DB::transaction(function () use (
            $wallet,
            $amount,
            $type,
            $description,
            $sourceType,
            $sourceId,
            $metadata,
            $status
        ) {
            // Create the transaction
            $transaction = WalletTransaction::create([
                'clinic_wallet_id' => $wallet->id,
                'amount' => $amount,
                'type' => $type,
                'status' => $status,
                'description' => $description,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'metadata' => $metadata,
            ]);

            // Update wallet balance if transaction is approved
            if ($status === 'approved') {
                if ($type === 'credit') {
                    $wallet->addBalance($amount);
                } elseif ($type === 'debit') {
                    $wallet->subtractBalance(abs($amount));
                }
            }

            return $transaction;
        });
    }

    /**
     * Credit a wallet with an amount.
     */
    public function creditWallet(
        ClinicWallet $wallet,
        int $amount,
        string $description,
        ?string $sourceType = null,
        ?string $sourceId = null,
        ?array $metadata = null
    ): WalletTransaction {
        return $this->recordTransaction(
            $wallet,
            $amount,
            'credit',
            $description,
            $sourceType,
            $sourceId,
            $metadata
        );
    }

    /**
     * Debit a wallet with an amount.
     */
    public function debitWallet(
        ClinicWallet $wallet,
        int $amount,
        string $description,
        ?string $sourceType = null,
        ?string $sourceId = null,
        ?array $metadata = null
    ): WalletTransaction {
        return $this->recordTransaction(
            $wallet,
            abs($amount), // Ensure positive amount for debit
            'debit',
            $description,
            $sourceType,
            $sourceId,
            $metadata
        );
    }

    /**
     * Get wallet transaction history.
     */
    public function getTransactionHistory(ClinicWallet $wallet, int $limit = 50): Collection
    {
        return $wallet->transactions()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get all wallets for a clinic.
     */
    public function getClinicWallets(Clinic $clinic): Collection
    {
        return $clinic->wallets()
            ->with('walletDefinition.vendor')
            ->get();
    }

    /**
     * Get wallet balance in dollars.
     */
    public function getWalletBalanceInDollars(ClinicWallet $wallet): float
    {
        return $wallet->balance_in_dollars;
    }
}
