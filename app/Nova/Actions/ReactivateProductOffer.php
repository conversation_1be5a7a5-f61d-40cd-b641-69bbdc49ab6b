<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Enums\ProductStockStatus;
use App\Models\ProductOffer;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;

final class ReactivateProductOffer extends Action
{
    public $name = 'Reactivate Product Offer';

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        ProductOffer::whereIn('id', $models->pluck('id'))->update([
            'stock_status' => ProductStockStatus::InStock,
            'deactivated_at' => null,
        ]);

        return ActionResponse::message('Product offers reactivated successfully!');
    }
}
