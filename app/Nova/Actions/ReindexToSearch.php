<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Models\Product;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;

final class ReindexToSearch extends Action
{
    public $name = 'Reindex to Search';

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        Product::whereIn('id', $models->pluck('id'))->searchable();

        return ActionResponse::message('Products reindexed to search successfully!');
    }
}
