<?php

declare(strict_types=1);

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

final class ClinicWallet extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\ClinicWallet>
     */
    public static $model = \App\Models\ClinicWallet::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'clinic.name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'clinic.name',
        'walletDefinition.name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, \Laravel\Nova\Fields\Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Clinic', 'clinic', Clinic::class)
                ->sortable()
                ->searchable()
                ->showOnPreview(),

            BelongsTo::make('Wallet Definition', 'walletDefinition', WalletDefinition::class)
                ->sortable()
                ->searchable()
                ->showOnPreview(),

            Currency::make('Balance', 'balance')
                ->currency('USD')
                ->asMinorUnits()
                ->sortable()
                ->showOnPreview(),

            HasMany::make('Transactions', 'transactions', WalletTransaction::class),
        ];
    }

    /**
     * Get the cards available for the resource.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<int, \Laravel\Nova\Filters\Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array<int, \Laravel\Nova\Lenses\Lens>
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array<int, \Laravel\Nova\Actions\Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
