<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\AccountRole;
use App\Modules\Account\Nova\ClinicAccount;
use App\Modules\Account\Nova\User;
use App\Nova\Actions\ImpersonateUser;
use App\Nova\Actions\LeaveImpersonation;
use App\Nova\Actions\SetupMoniteIntegration;
use App\Nova\Actions\ToggleMoniteFeatureFlag;
use App\Nova\Fields\MoniteStatusBadge;
use Illuminate\Database\Eloquent\Builder;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\MorphOne;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Http\Requests\NovaRequest;
use Illuminate\Validation\Rule;
use <PERSON><PERSON>\Nova\Fields\HasMany;

final class Clinic extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Clinic>
     */
    public static $model = \App\Models\Clinic::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name', 'business_tax_id',
    ];

    /**
     * Filter out administrators from the users list.
     */
    public static function relatableUsers(NovaRequest $request, Builder $query)
    {
        return $query->whereNot('role', AccountRole::Administrator);
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make('ID', 'id')
                ->hideFromIndex()
                ->sortable(),

            Text::make('Name', 'name')
                ->sortable()
                ->rules('required', 'max:255'),

            BelongsTo::make('Account', 'account', ClinicAccount::class)
                ->readonly(fn (NovaRequest $request) => $request->isUpdateOrUpdateAttachedRequest())
                ->searchable(),

            Text::make('Business Tax ID', 'business_tax_id')
                ->sortable()
                ->rules('required'),

            Text::make('Phone Number', 'phone_number')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('Monite Entity ID', 'monite_entity_id')
                ->sortable()
                ->readonly()
                ->hideFromIndex()
                ->help('The corresponding Monite entity identifier for this clinic'),

            MoniteStatusBadge::make('Monite Status')
                ->onlyOnDetail(),

            Heading::make('GPO')
                ->canSee(function ($request) {
                    $clinic = $this->resource;

                    if (! $clinic || ! $clinic->exists) {
                        return false;
                    }

                    return $clinic->account()->first()->gpo;
                }),

            Text::make('GPO Membership Reference', 'gpo_membership_reference')
                ->nullable()
                ->rules(function ($request) {
                    $clinic = $this->resource; // pega o modelo atual

                    return [
                        function ($attribute, $value, $fail) use ($clinic) {
                            $gpoId = optional($clinic->account)->gpo?->id;

                            $exists = \App\Models\Clinic::where('gpo_membership_reference', $value)
                                ->whereHas('account', fn($q) => $q->where('gpo_account_id', $gpoId))
                                ->where('id', '!=', $clinic->id)
                                ->exists();

                            if ($exists) {
                                $fail('This reference already exists for this GPO.');
                            }
                        },
                    ];
                })
                ->canSee(function ($request) {
                    $clinic = $this->resource;

                    if (! $clinic || ! $clinic->exists) {
                        return false;
                    }

                    return $clinic->account()->first()->gpo;
                })
                ->hideFromIndex()
            ,

            Date::make('GPO Membership Since', 'gpo_membership_since')
                ->nullable()
                ->rules(function ($request) {
                    return [
                        'date',
                        'before_or_equal:today',
                    ];
                })
                ->canSee(function ($request) {
                    $clinic = $this->resource;

                    if (! $clinic || ! $clinic->exists) {
                        return false;
                    }

                    return $clinic->account()->first()->gpo;
                })
                ->hideFromIndex()
            ,


            MorphOne::make('Shipping Address', 'shippingAddress', Address::class),

            MorphOne::make('Billing Address', 'billingAddress', Address::class),

            HasOne::make('Budget Settings', 'budgetSettings', ClinicBudgetSettings::class),

            BelongsToMany::make('Users', 'users', User::class),

            BelongsToMany::make('Products', 'productOffers', ProductOffer::class)
                ->searchable()
                ->fields(fn () => [
                    Currency::make('Price', 'price')
                        ->currency('USD')
                        ->asMinorUnits()
                        ->sortable()
                        ->required()
                        ->min(0)
                        ->step(0.01),
                ]),

            HasMany::make('Wallets', 'wallets', ClinicWallet::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            resolve(ImpersonateUser::class)
                ->sole()
                ->showInline(),
            resolve(LeaveImpersonation::class)
                ->onlyOnIndex()
                ->standalone()
                ->canSee(fn () => session()->has('impersonate')),
            new ToggleMoniteFeatureFlag(),
            new SetupMoniteIntegration(),
        ];
    }
}
