<?php

declare(strict_types=1);

namespace App\Nova;

use App\Modules\Product\Nova\ProductBrand;
use App\Modules\Product\Nova\ProductCategory;
use App\Modules\Product\Nova\ProductManufacturer;
use App\Modules\Product\Nova\ProductTag;
use App\Nova\Actions\ReindexToSearch;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Image;
use Laravel\Nova\Fields\Markdown;
use Laravel\Nova\Fields\Tag;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Panel;

final class Product extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Product>
     */
    public static $model = \App\Models\Product::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'manufacturer_sku',
        'productOffers.name',
        'productOffers.vendor_sku',
    ];

    public static function usesScout(): bool
    {
        return false;
    }

    public static function relatableProductCategories(NovaRequest $request, $query)
    {
        return $query->whereDoesntHave('children');
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->help('The name of the product. This will be used to identify the product in the system.')
                ->sortable()
                ->rules('required', 'string', 'max:255'),

            BelongsTo::make('Category', 'category', ProductCategory::class)
                ->nullable()
                ->searchable()
                ->withSubtitles(),

            Tag::make('Tags', 'tags', ProductTag::class)
                ->nullable()
                ->searchable(),

            BelongsTo::make('Manufacturer', 'manufacturer', ProductManufacturer::class)
                ->nullable()
                ->searchable(),

            Text::make('Manufacturer SKU', 'manufacturer_sku')
                ->help('The manufacturer sku of product.')
                ->sortable()
                ->rules('required', 'string', 'max:255'),

            BelongsTo::make('Brand', 'brand', ProductBrand::class)
                ->nullable()
                ->searchable(),

            // Text::make('SKU', 'sku')
            //     ->exceptOnForms(),

            Markdown::make('Description', 'description')
                ->required()
                ->hideFromIndex(),

            Text::make('Image URL', 'image_url')
                ->required()
                ->rules('url')
                ->onlyOnForms(),

            Image::make('Image', 'image_url')
                ->exceptOnForms()
                ->disableDownload()
                ->preview(fn (string $value) => $value)
                ->thumbnail(fn (string $value) => $value)
                ->prunable()
                ->deletable()
                ->creationRules('required', 'image', 'max:1024')
                ->updateRules('nullable', 'image', 'max:1024')
                ->indexWidth(64)
                ->squared(),

            Boolean::make('Is Hazardous', 'is_hazardous')
                ->help('Whether the product is hazardous or not.')
                ->sortable()
                ->rules('required', 'boolean'),

            Boolean::make('Requires Prescription', 'requires_prescription')
                ->help('Whether the product requires prescription or not.')
                ->sortable()
                ->rules('required', 'boolean'),

            Boolean::make('Requires Cold Shipping', 'requires_cold_shipping')
                ->help('Whether the product requires cold shipping or not.')
                ->sortable()
                ->rules('required', 'boolean'),

            Boolean::make('Is Controlled substance', 'is_controlled_substance')
                ->help('Whether the product is controlled substance or not.')
                ->sortable()
                ->rules('required', 'boolean'),

            Boolean::make('Is Controlled 222 Form', 'is_controlled_222_form')
                ->help('Whether the product is controlled 222 form or not.')
                ->sortable()
                ->rules('required', 'boolean'),

            Boolean::make('Requires pedigree', 'requires_pedigree')
                ->help('Whether the product requires pedigree or not.')
                ->sortable()
                ->rules('required', 'boolean'),

            Text::make('National Drug Code', 'national_drug_code')
                ->nullable()
                ->rules('nullable', 'regex:/^\d{4,6}-[A-Za-z0-9]{3,4}-[A-Za-z0-9]{1,2}$/')
                ->help('Format: 12345-6789-0'),

            Text::make('Offers Count', fn () => $this->productOffers()->count())
                ->onlyOnIndex(),

            HasMany::make('Offers', 'productOffers', ProductOffer::class),

            HasMany::make('Alternatives', 'alternatives', ProductAlternative::class)
                ->hideFromIndex(),

            Panel::make('Attributes', [
                HasMany::make('Attributes', 'attributes', ProductAttribute::class)
                    ->hideFromIndex(),
            ]),

            Panel::make('Search Terms', [
                HasMany::make('Search Terms', 'searchTerms', ProductSearchTerm::class)
                    ->hideFromIndex(),
            ]),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            new ReindexToSearch(),
        ];
    }
}
