<?php

declare(strict_types=1);

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

final class WalletDefinition extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\WalletDefinition>
     */
    public static $model = \App\Models\WalletDefinition::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name',
        'description',
        'vendor.name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, \Laravel\Nova\Fields\Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Vendor', 'vendor', Vendor::class)
                ->sortable()
                ->searchable()
                ->rules('required')
                ->showOnPreview(),

            Text::make('Name', 'name')
                ->sortable()
                ->rules('required', 'max:255')
                ->showOnPreview(),

            Textarea::make('Description', 'description')
                ->nullable()
                ->rules('nullable', 'max:1000')
                ->help('Optional description of the wallet definition')
                ->showOnPreview(),

            Code::make('Settings', 'settings')
                ->json()
                ->rules('required')
                ->help('Define wallet rules, expiry settings, and redemption logic as JSON')
                ->showOnPreview(),

            Boolean::make('Active', 'is_active')
                ->sortable()
                ->rules('required')
                ->showOnPreview(),
        ];
    }

    /**
     * Get the cards available for the resource.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<int, \Laravel\Nova\Filters\Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array<int, \Laravel\Nova\Lenses\Lens>
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array<int, \Laravel\Nova\Actions\Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
