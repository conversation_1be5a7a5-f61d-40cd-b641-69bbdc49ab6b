<?php

declare(strict_types=1);

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\Badge;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\Currency;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class WalletTransaction extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\WalletTransaction>
     */
    public static $model = \App\Models\WalletTransaction::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'description';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'description',
        'source_type',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, \Laravel\Nova\Fields\Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Clinic Wallet', 'clinicWallet', ClinicWallet::class)
                ->sortable()
                ->searchable()
                ->showOnPreview(),

            Currency::make('Amount', 'amount')
                ->currency('USD')
                ->asMinorUnits()
                ->sortable()
                ->showOnPreview(),

            Select::make('Type', 'type')
                ->options([
                    'credit' => 'Credit',
                    'debit' => 'Debit',
                ])
                ->displayUsingLabels()
                ->sortable()
                ->showOnPreview(),

            Badge::make('Status', 'status')
                ->map([
                    'pending' => 'warning',
                    'approved' => 'success',
                    'rejected' => 'danger',
                ])
                ->sortable()
                ->showOnPreview(),

            Text::make('Description', 'description')
                ->sortable()
                ->showOnPreview(),

            Text::make('Source Type', 'source_type')
                ->sortable()
                ->showOnPreview(),

            Text::make('Source ID', 'source_id')
                ->sortable()
                ->showOnPreview(),

            Code::make('Metadata', 'metadata')
                ->json()
                ->hideFromIndex()
                ->showOnPreview(),

            DateTime::make('Created At', 'created_at')
                ->sortable()
                ->showOnPreview(),
        ];
    }

    /**
     * Get the cards available for the resource.
     *
     * @return array<int, \Laravel\Nova\Card>
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array<int, \Laravel\Nova\Filters\Filter>
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array<int, \Laravel\Nova\Lenses\Lens>
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array<int, \Laravel\Nova\Actions\Action>
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
