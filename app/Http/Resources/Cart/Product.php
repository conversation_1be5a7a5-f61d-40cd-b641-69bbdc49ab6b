<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use App\Actions\Products\GetRebateEstimateByProductOffer;
use App\Http\Resources\Products\Attribute;
use App\Models\Clinic;
use App\Models\ClinicProductFavorite;
use App\Models\ProductOffer as ProductOfferModel;
use App\Services\Sorting\ProductOfferSorter;
use Brick\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class Product extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $rebateEstimateByProductOfferAction = app(GetRebateEstimateByProductOffer::class);

        $clinicIds = $this->productOffers->flatMap(function (ProductOfferModel $product) {
            return $product->clinics->pluck('id');
        })->unique()->values()->toArray();

        return [
            /**
             * The product's unique identifier.
             */
            'id' => $this->id,

            /**
             * The product's display name.
             */
            'name' => $this->name,

            /**
             * The URL to the product's primary image.
             */
            'imageUrl' => $this->image_url,

            /**
             * Whether the product is marked as a favorite by the clinic.
             */
            'isFavorite' => ClinicProductFavorite::where('clinic_id', $request->clinicId())
                ->where('product_id', $this->id)->exists(),

            /**
             * The name of the product's manufacturer/brand.
             */
            /*'manufacturer' => $this->manufacturer ? [
                'id' => $this->manufacturer->id,
                'name' => $this->manufacturer->name,
            ] : null,*/
            'manufacturer' => $this->manufacturer?->name,

            /**
             * The unique identifier assigned by the manufacturer for this product.
             */
            'manufacturerSku' => $this->manufacturer_sku,

            /**
             * Detailed product description including features and specifications.
             */
            'description' => $this->description,

            /**
             * The product offers from product.
             */
            'offers' => ProductOfferSorter::sort($this->productOffers, $request->clinicId())
                ->map(function (ProductOfferModel $productOffer) use ($request, $clinicIds, $rebateEstimateByProductOfferAction) {
                    $clinic = $productOffer->clinics->whereIn('id', $clinicIds)->first();
                    $clinicPrice = $clinic?->pivot?->price;
                    $price = $productOffer->price;

                    return [
                        'id' => $productOffer->id,
                        'vendor' => [
                            'id' => $productOffer->vendor->id,
                            'name' => $productOffer->vendor->name,
                            'imageUrl' => asset("storage/{$productOffer->vendor->image_path}"),
                            'type' => $productOffer->vendor->type,
                        ],
                        'isPurchasable' => $productOffer->is_purchasable,
                        'vendorSku' => $productOffer->vendor_sku,
                        'price' => ! is_null($price) ? Money::ofMinor($price, 'USD')->getAmount() : null,
                        'clinicPrice' => ! is_null($clinicPrice) ? Money::ofMinor($clinicPrice, 'USD')->getAmount() : null,
                        'stockStatus' => $productOffer->stock_status,
                        'lastOrderedAt' => $productOffer->orderItems?->where('created_at', '>=', now()->subDays(90))->first()?->created_at,
                        'lastOrderedQuantity' => $productOffer->orderItems?->where('created_at', '>=', now()->subDays(90))->first()?->quantity,
                        'increments' => $productOffer->increments ?? 1,
                        'isRecommended' => $productOffer->is_recommended ?? false,
                        'rebatePercent' => $rebateEstimateByProductOfferAction->handle($request->clinicId(), $productOffer->id)?->current_rebate_percent,
                        'unitOfMeasure' => $productOffer->unit_of_measure,
                        'size' => $productOffer->size,
                        'raw_category_1' => $productOffer->raw_category_1,
                        'raw_category_2' => $productOffer->raw_category_2,
                        'raw_category_3' => $productOffer->raw_category_3,
                        'raw_category_4' => $productOffer->raw_category_4,

                    ];
                })
                ->filter(function ($offer) {
                    return ! is_null($offer['price']) || ! is_null($offer['clinicPrice']);
                })
                ->values()
                ->toArray(),

            'attributes' => $this->whenLoaded('attributes', fn () => Attribute::collection($this->attributes)),
            /** Product Flags */
            'isHazardous' => $this->is_hazardous,
            'requiresPrescription' => $this->requires_prescription,
            'requiresColdShipping' => $this->requires_cold_shipping,
            'isControlledSubstance' => $this->is_controlled_substance,
            'isControlled222Form' => $this->is_controlled_222_form,
            'requiresPedigree' => $this->requires_pedigree,
            'nationalDrugCode' => $this->national_drug_code,
        ];
    }
}
