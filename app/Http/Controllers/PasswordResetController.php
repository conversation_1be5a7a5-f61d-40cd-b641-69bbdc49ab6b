<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\SendPasswordResetLink;
use App\Http\Requests\PasswordResetRequest;
use App\Models\User;
use Illuminate\Http\Response;

final class PasswordResetController extends Controller
{
    /**
     * Handle an incoming password reset request.
     */
    public function store(
        PasswordResetRequest $request,
        SendPasswordResetLink $action
    ): Response {
        if (! User::where('email', $request->validated('email'))->exists()) {
            return response()->noContent();
        }

        $action->handle($request->validated());

        return response()->noContent();
    }
}
