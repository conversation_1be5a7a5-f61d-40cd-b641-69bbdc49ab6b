<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Enums\ProductStockStatus;
use App\Http\Controllers\Controller;
use App\Modules\Order\Data\PreviouslyOrderedItem;
use App\Modules\Order\Data\PreviouslyPurchasedItemData;
use App\Modules\Order\Queries\PreviouslyOrderedItemsQuery;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Spatie\LaravelData\PaginatedDataCollection;

final class PreviouslyPurchasedController extends Controller
{
    /**
     * Get previously purchased items for a clinic with pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $paginatedResults = PreviouslyOrderedItemsQuery::for($request->clinicId())->jsonPaginate();

        $transformedCollection = $paginatedResults->getCollection()->map(function ($item) {
            return new PreviouslyOrderedItem(
                $item->product_offer_id,
                $item->product_id,
                $item->product_name,
                $item->vendor_id,
                $item->vendor_name,
                $item->vendor_image_path,
                $item->vendor_sku,
                $item->product_offer_name,
                $item->size,
                $item->unit_of_measure,
                (bool) ($item->is_recommended ?? false),
                $item->product_offer_price,
                $item->clinic_price,
                Carbon::parse($item->last_ordered_at),
                $item->total_quantity,
                $item->price,
                $item->image_url ?? '',
                $item->order_count,
                $status = ProductStockStatus::from($item->stock_status),
                $item->increments,
                $item->deactivated_at === null && $status !== ProductStockStatus::Discontinued,
            );
        });

        $paginatedResults->setCollection($transformedCollection);

        // Transform to PreviouslyPurchasedItemData with clinic ID for rebate estimates
        $clinicId = $request->clinicId();
        $dataCollection = $transformedCollection->map(function ($item) use ($clinicId) {
            return PreviouslyPurchasedItemData::fromPreviouslyOrderedItem($item, $clinicId);
        });

        $paginatedResults->setCollection($dataCollection);

        $data = PreviouslyPurchasedItemData::collect(
            $paginatedResults,
            PaginatedDataCollection::class
        );

        return response()->json($data);
    }
}
