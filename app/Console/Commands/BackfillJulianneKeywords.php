<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Product;
use Illuminate\Console\Command;

class BackfillJulianneKeywords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:backfill-julianne-keywords';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add search terms for products based on Julianne keyword list';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Processing keywords...');

        $processedProducts = 0;
        $addedTerms = 0;
        $notFoundProducts = [];

        // Process unique keywords (one-to-one mapping)
        foreach ($this->uniqueKeywords() as $productName => $searchTerm) {
            $result = $this->processProduct($productName, [$searchTerm]);
            $processedProducts += $result['processed'];
            $addedTerms += $result['added'];
            if ($result['notFound']) {
                $notFoundProducts[] = $productName;
            }
        }

        // Process multiple keywords (one-to-many mapping)
        foreach ($this->multipleKeywords() as $productName => $searchTerms) {
            $result = $this->processProduct($productName, $searchTerms);
            $processedProducts += $result['processed'];
            $addedTerms += $result['added'];
            if ($result['notFound']) {
                $notFoundProducts[] = $productName;
            }
        }

        $this->newLine();
        $this->info('Processing completed!');
        $this->info("Products processed: {$processedProducts}");
        $this->info("Terms added: {$addedTerms}");

        if (! empty($notFoundProducts)) {
            $this->newLine();
            $this->warn('Products not found:');
            foreach ($notFoundProducts as $productName) {
                $this->warn("- {$productName}");
            }
        }

        return Command::SUCCESS;
    }

    private function processProduct(string $productName, array $searchTerms): array
    {
        // Find product by name (case insensitive, partial match using PostgreSQL ILIKE)
        $product = Product::query()
            ->whereRaw('name ILIKE ?', ['%'.$productName.'%'])
            ->first();

        if (! $product) {
            return ['processed' => 0, 'added' => 0, 'notFound' => true];
        }

        $addedCount = 0;
        $hasNewTerms = false;
        
        foreach ($searchTerms as $searchTerm) {
            // Check if search term already exists (using PostgreSQL ILIKE)
            $existingTerm = $product->searchTerms()
                ->whereRaw('term ILIKE ?', [$searchTerm])
                ->first();

            if (! $existingTerm) {
                $product->searchTerms()->create([
                    'term' => $searchTerm,
                ]);
                $addedCount++;
                $hasNewTerms = true;
                $this->info("Added term '{$searchTerm}' for product '{$product->name}'");
            } else {
                $this->warn("Term '{$searchTerm}' already exists for product '{$product->name}'");
            }
        }

        // Force Scout to reindex the product if new terms were added
        if ($hasNewTerms && $product->shouldBeSearchable()) {
            $product->searchable();
            $this->info("Reindexed product '{$product->name}' in search engine");
        }

        return ['processed' => 1, 'added' => $addedCount, 'notFound' => false];
    }

    private function uniqueKeywords(): array
    {
        return [
            'Acepromazine' => 'PromAce',
            'Alprazolam' => 'Xanax',
            'Amlodipine Besylate' => 'Norvasc',
            'Atenolol' => 'Tenormin',
            'Azathioprine' => 'Imuran',
            'Benazapril' => 'Lotensin',
            'Budesonide' => 'Entocord',
            'Burprenorphine' => 'Buprenex',
            'Buspirone Hydrochloride' => 'Buspar',
            'Calcitriol' => 'Rocaltrol',
            'Cephalexin' => 'Keflex',
            'Cetirizine Hydrochloride' => 'Zyrtec',
            'Chlorambucil' => 'Leukeran',
            'Chlorpheniramine Maleate' => 'Chlor-Trimeton',
            'Cimetidine' => 'Tagamet',
            'Cisapride' => 'Propulsid',
            'Clopidogrel Bisulfate' => 'Plavix',
            'Colchicine' => 'Colcrys',
            'Cyclophosphamide' => 'Cytoxan',
            'Diazepam' => 'Valium',
            'Diethylstilbestrol' => 'DES',
            'Diltiazem' => 'Cardizem',
            'Doxycycline' => 'Vibramycin',
            'Epsilon Aminocaproic Acid' => 'Amicar',
            'Esomeprazole Magnesium' => 'Nexium',
            'Erythropoetin' => 'EPO',
            'Famciclovir' => 'Famvir',
            'Fenbendazole' => 'Panacur',
            'Fentanyl' => 'Duragesic',
            'Finasteride' => 'Propecia',
            'Fluconazole' => 'Diflucan',
            'Fludrocortisone Acetate' => 'Florinef',
            'Fluoxetine' => 'Prozac',
            'Gabapentin' => 'Neurontin',
            'Glargine' => 'Lantus',
            'Detemir' => 'Levemir',
            'Glipizide' => 'Glucotrol',
            'Griseofulvin' => 'Fulvicin',
            'Hydroxizine' => 'Atarax',
            'Itraconazole' => 'Sporonox',
            'Ketoconazole' => 'Nizoral',
            'Lactulose' => 'Enulose',
            'L-Asparaginase' => 'Elspar',
            'levothyroxine' => 'thyroid',
            'Loperamide' => 'Imodium AD',
            'Loaratadine' => 'Claritin',
            'Methocarbamol' => 'Robaxin',
            'Metoclopramide' => 'Reglan',
            'Metronidazole' => 'Flagyl',
            'Mirtazapine' => 'Remeron',
            'Oseltamivir' => 'Tamiflu',
            'Pamidronate Disodium' => 'Aredia',
            'Pentoxifylline' => 'Trental',
            'Phenylpropanolamine' => 'PPA',
            'Pregabalin' => 'Lyrica',
            'Ranitidine' => 'Zantac',
            'Sertraline' => 'Zoloft',
            'Silymarin' => 'Milk Thistle',
            'Sucralfate' => 'Carafate',
            'Sulfasalazine' => 'Azulfidine',
            'Terbinafine' => 'Lamisil',
            'Terbutaline Sulfate' => 'Brethine',
            'Ursodiol' => 'Actigall',
            'Aspirator' => 'suction',
            'Infusion pump' => 'IV pump',
            'bouffant caps' => 'surgery caps',
            'clean air canister' => 'F Air',
            'positioner' => 'v trough',
            'percussion hammer' => 'reflex hammer',
            'polypropalene cath' => 'PPA cath',
            'male adapter' => 'injection plug',
            'extension set' => 'ext set',
            'IV winged infusion set' => 'butterfly cath',
            'lactated ringers' => 'LRS',
            'sodium chloride injection' => 'saline',
            'normosol' => 'plasmalyte',
            'smart y' => 'stopcock',
            'elastic tape' => 'elastikon',
            'cotton tip applicator' => 'q tip',
            'lead' => 'leash',
        ];
    }

    private function multipleKeywords(): array
    {
        return [
            'Amoxicillin Clavulanic Acid' => [
                'Clavamox',
                'Augmentin',
                'Clavacillin',
                'Umbrellin',
                'Amoxi Clav',
            ],
            'Azithromycin' => [
                'Zythromax',
                'Zithromax',
            ],
            'Butorphanol' => [
                'Torbutrol',
                'Torbugesic',
                'Dolorex',
                'Torb',
                'Butorphic',
            ],
            'Chloramphenicol' => [
                'Chloromycetin',
                'CHPC',
            ],
            'Clindamycin Hydrochloride' => [
                'Clindadrops',
                'Antirobe',
                'Cleosin',
                'Clindamicin',
            ],
            'Diphenhydramine' => [
                'Benadryl',
                'Vetadryl',
                'Banophen',
                'Diphenhist',
            ],
            'Diphenoxylate Hydrochloride' => [
                'Lomotil',
                'Lonox',
                'Lomanate',
            ],
            'Doxorubicin' => [
                'Adriamycin',
                'Rubex',
            ],
            'cohesive wrap' => [
                'rapz',
                'pet flex',
                'flex',
                'coflex bandages',
                'vetrap',
            ],
            'Dexamethasone' => [
                'Azium',
                'Vorren',
            ],
            'Dexmedetomidine' => [
                'Sileo',
                'Dexdomitor',
                'dexmedtitomidine',
            ],
            'Enalapril Maleate' => [
                'Enacard',
                'Vasotec',
            ],
            'Erythromycin' => [
                'Ery-tab',
                'Ery-ped',
                'Eryc',
            ],
            'Etodolac' => [
                'Etogesic',
                'Lodine',
            ],
            'Famotidine' => [
                'Pepcid',
                'Pepcid AC',
                'Pepcid RPD',
            ],
            'Furosemide' => [
                'Lasix',
                'Salix',
                'Disal',
            ],
            'Hydrocodone Bitartrate' => [
                'Hycodan',
                'Tussigon',
                'Mycodone',
            ],
            'Interferon' => [
                'Intron A',
                'Alferon N',
            ],
            'Levetiracetam' => [
                'Keppra',
                'Keppra XR',
                'Kepcet',
                'Kerron',
                'Kevtan',
                'Levitaccord',
                'Levitam',
            ],
            'Meclizine Hydrochloride' => [
                'Bonine',
                'Antivert',
                'Dramamine',
                'Univert',
                'Vertin 21',
            ],
            'Patient Warming' => [
                'Warm Touch',
                'Hot Dog',
                'Bair Huggar',
                'Convection heat',
                'chill buster',
                'Equator',
                'Vet Pro',
                'warming system',
                'patient heater',
                'Vet Bedjet',
                'Cloud 9',
                'Cocoon',
                'Hover heat',
                'carbon tech',
                'warm water',
            ],
            'elizabethan collar' => [
                'e collar',
            ],
            't-port' => [
                't-connector',
                't extension set',
            ],
            'IV set' => [
                'primary iv',
                'administration set',
            ],
            'Capromorelin' => [
                'Entyce',
                'Entice',
            ],
            'Cefpodoxime Proxetil' => [
                'Simplicef',
                'Vantin',
            ],
            'Omeprazole' => [
                'Prilosec',
                'Gastrogard',
            ],
            'S-Adenosylmethionine' => [
                'SAMe',
                'Novifit',
            ],
            'Trimethoprim Sulfamethoxazole' => [
                'SMZ TMP',
                'TMP SMZ',
            ],
            'S-Adenosylmethionine' => [
                'SAMe',
                'Novifit',
            ],
        ];
    }
}
