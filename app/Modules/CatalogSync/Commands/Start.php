<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Commands;

use App\Enums\SyncFrequency;
use App\Modules\CatalogSync\Actions\CreateCatalogSyncTask;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

final class Start extends Command
{
    protected $signature = 'catalog-sync:start {--vendor=} {--clinic=}';

    protected $description = 'Start the catalog sync process';

    public function handle(CreateCatalogSyncTask $action)
    {
        $vendorId = $this->option('vendor');
        $clinicId = $this->option('clinic');

        $this->info('Starting the catalog sync process');

        $page = Carbon::now()->dayOfWeek() + 1;
        $now = Carbon::now();

        $query = IntegrationConnection::query()
            ->with('vendor')
            ->whereHas('vendor', function (Builder $query) {
                $query->whereJsonContains('integration_points', IntegrationPoint::SyncProductCatalog);
            })
            ->whereIn('status', [IntegrationConnectionStatus::Connecting, IntegrationConnectionStatus::Connected])
            ->whereNotNull('credentials')
            ->where(function (Builder $query) use ($now) {
                $query->where(function (Builder $subQuery) use ($now) {
                    // Daily sync - last sync was more than 1 day ago or never synced
                    $subQuery->whereHas('vendor', function (Builder $vendorQuery) {
                        $vendorQuery->where('sync_frequency', SyncFrequency::Daily);
                    })
                    ->where(function (Builder $syncQuery) use ($now) {
                        $syncQuery->whereNull('last_sync_at')
                                 ->orWhere('last_sync_at', '<', $now->subDay());
                    });
                })
                ->orWhere(function (Builder $subQuery) use ($now) {
                    // Weekly sync - last sync was more than 1 week ago or never synced
                    $subQuery->whereHas('vendor', function (Builder $vendorQuery) {
                        $vendorQuery->where('sync_frequency', SyncFrequency::Weekly);
                    })
                    ->where(function (Builder $syncQuery) use ($now) {
                        $syncQuery->whereNull('last_sync_at')
                                 ->orWhere('last_sync_at', '<', $now->subWeek());
                    });
                })
                ->orWhere(function (Builder $subQuery) use ($now) {
                    // Monthly sync - last sync was more than 1 month ago or never synced
                    $subQuery->whereHas('vendor', function (Builder $vendorQuery) {
                        $vendorQuery->where('sync_frequency', SyncFrequency::Monthly);
                    })
                    ->where(function (Builder $syncQuery) use ($now) {
                        $syncQuery->whereNull('last_sync_at')
                                 ->orWhere('last_sync_at', '<', $now->subMonth());
                    });
                });
            });

        if ($vendorId) {
            $query->where('vendor_id', $vendorId);
        }

        if ($clinicId) {
            $query->where('clinic_id', $clinicId);
        }

        $count = $query->count();

        $this->line("There are {$count} clinic-vendors connections ready for sync");

        $connections = $vendorId || $clinicId ? $query->get() : $query->paginate($count / 3, page: $page);

        foreach ($connections as $connection) {
            $action->handle($connection->clinic_id, $connection->vendor_id);
            
            // Update last_sync_at timestamp
            $connection->update(['last_sync_at' => $now]);
        }

        $this->info("The catalog sync process has started for {$connections->count()} clinic-vendors connections");
    }
}
