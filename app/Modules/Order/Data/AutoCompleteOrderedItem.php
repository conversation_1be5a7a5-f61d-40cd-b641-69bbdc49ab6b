<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Enums\ProductStockStatus;
use Carbon\Carbon;

final readonly class AutoCompleteOrderedItem
{
    public function __construct(
        public string $product_offer_id,
        public string $product_id,
        public string $product_name,
        public string $vendor_id,
        public string $vendor_name,
        public Carbon $last_ordered_at,
        public int $quantity,
        public int $price,
        public string $image_url,
        public int $order_count,
        public ProductStockStatus $stock_status,
        public int $increments,
        public bool $is_purchasable,
    ) {}
}
