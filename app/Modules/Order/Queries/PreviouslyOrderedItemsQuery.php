<?php

declare(strict_types=1);

namespace App\Modules\Order\Queries;

use App\Models\OrderItem;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;
use Spatie\QueryBuilder\QueryBuilder;

final class PreviouslyOrderedItemsQuery extends QueryBuilder
{
    public function __construct(string $clinicId)
    {
        $query = OrderItem::query()
            ->select([
                'product_offers.id as product_offer_id',
                'products.id as product_id',
                'products.name as product_name',
                'products.image_url',
                'vendors.id as vendor_id',
                'vendors.name as vendor_name',
                'vendors.image_path as vendor_image_path',
                'product_offers.vendor_sku',
                'product_offers.stock_status',
                'product_offers.increments',
                'product_offers.deactivated_at',
                'product_offers.name as product_offer_name',
                'product_offers.size',
                'product_offers.unit_of_measure',
                'product_offers.price as product_offer_price',
                DB::raw('(gpo_recommended_products.product_offer_id IS NOT NULL) as is_recommended'),
                'clinic_product_offer.price as clinic_price',
                DB::raw('MAX(order_items.created_at) as last_ordered_at'),
                DB::raw('SUM(order_items.quantity) as total_quantity'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                'order_items.price',
            ])
            ->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->join('product_offers', 'product_offers.id', '=', 'order_items.product_offer_id')
            ->join('products', 'products.id', '=', 'product_offers.product_id')
            ->join('vendors', 'vendors.id', '=', 'product_offers.vendor_id')
            ->leftJoin('clinic_product_offer', function ($join) use ($clinicId) {
                $join->on('clinic_product_offer.product_offer_id', '=', 'product_offers.id')
                    ->where('clinic_product_offer.clinic_id', $clinicId);
            })
            ->leftJoin('clinics', 'clinics.id', '=', DB::raw("'{$clinicId}'"))
            ->leftJoin('accounts', 'accounts.id', '=', 'clinics.clinic_account_id')
            ->leftJoin('gpo_recommended_products', function ($join) {
                $join->on('gpo_recommended_products.product_offer_id', '=', 'product_offers.id')
                    ->on('gpo_recommended_products.gpo_account_id', '=', 'accounts.gpo_account_id');
            })
            ->join('integration_connections', function ($join) use ($clinicId) {
                $join->on('integration_connections.vendor_id', '=', 'vendors.id')
                    ->where('integration_connections.clinic_id', $clinicId)
                    ->where('integration_connections.status', IntegrationConnectionStatus::Connected->value);
            })
            ->where('orders.clinic_id', $clinicId)
            ->groupBy([
                'product_offers.id',
                'products.id',
                'products.name',
                'products.image_url',
                'vendors.id',
                'vendors.name',
                'vendors.image_path',
                'product_offers.vendor_sku',
                'product_offers.stock_status',
                'product_offers.increments',
                'product_offers.name',
                'product_offers.size',
                'product_offers.unit_of_measure',
                'product_offers.price',
                'clinic_product_offer.price',
                DB::raw('(gpo_recommended_products.product_offer_id IS NOT NULL)'),
                'order_items.price',
            ]);

        parent::__construct($query);

        $this->allowedFilters([
            AllowedFilter::callback('search', function (Builder $query, $value) {
                $searchTerms = explode(' ', $value);
                foreach ($searchTerms as $term) {
                    $term = mb_trim($term);
                    if (! empty($term)) {
                        $query->where(function ($q) use ($term) {
                            $q->where('products.name', 'ilike', "%{$term}%")
                                ->orWhere('product_offers.name', 'ilike', "%{$term}%");
                        });
                    }
                }
                return $query;
            }),
        ]);

        $this->allowedSorts([
            AllowedSort::field('last_ordered', 'last_ordered_at'),
        ]);

        $this->defaultSort([
            AllowedSort::field('-last_ordered', 'last_ordered_at'),
        ]);
    }
}
