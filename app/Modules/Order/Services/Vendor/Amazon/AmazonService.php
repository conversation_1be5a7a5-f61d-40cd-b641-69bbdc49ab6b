<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\Amazon;

use App\Enums\OrderItemStatus;
use App\Exceptions\VendorServiceError;
use App\Models\OrderItem;
use App\Models\SubOrder;
use App\Modules\Integration\Enums\IntegrationEventStatus;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Enums\IntegrationSessionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Integration\Models\IntegrationSession;
use App\Modules\Order\Enums\VendorError;
use App\Modules\Order\Services\Vendor\Contracts\OrderProcessor;
use App\Modules\Order\Services\Vendor\Contracts\OrderReconciler;
use App\Modules\Order\Services\Vendor\Contracts\OrderSynchronizer;
use App\Modules\Order\Services\Vendor\Contracts\ShipmentSynchronizer;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use SensitiveParameter;

final class AmazonService implements OrderProcessor, OrderReconciler, OrderSynchronizer, ShipmentSynchronizer
{
    private AmazonHttpClient $client;

    /**
     * Create a new Amazon instance.
     */
    public function __construct(
        #[SensitiveParameter]
        private readonly string $refreshToken,
        private readonly string $buyingGroupId,
    ) {
        $this->client = new AmazonHttpClient(
            $this->refreshToken
        );
    }

    /**
     * Place an order with the vendor.
     *
     * @throws VendorServiceError
     */
    public function processOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::PlaceOrders,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            $orderData = $this->createOrderPayload($suborder);
            $response = $this->client->placeOrder($orderData, $session, 'place_order', $suborder)->data;
            $lineItems = collect($response['lineItems'] ?? []);

            $lineItemsErrors = [];
            $lineItems->each(function ($lineItem) use ($suborder, $session, &$lineItemsErrors) {
                $item = $suborder->items->firstWhere('id', $lineItem['externalId']);
                if (! $item) {
                    return;
                }
                if (! empty($lineItem['rejectedItems'])) {
                    $lineItemsErrors[] = $lineItem['rejectedItems'];
                    $item->update([
                        'status' => OrderItemStatus::PlacementFailed,
                        'error_message' => json_encode($lineItem['rejectedItems'], JSON_PRETTY_PRINT),
                    ]);
                    $session->events()->create([
                        'action' => 'order_item_creation_failed',
                        'metadata' => $lineItem['rejectedItems'],
                        'subject_type' => OrderItem::class,
                        'subject_id' => $item->id,
                        'status' => IntegrationEventStatus::Error,
                    ]);

                    return;
                }
                $item->update([
                    'status' => OrderItemStatus::Accepted,
                    'error_message' => null,
                ]);
            });

            if (! empty($response['rejectionArtifacts']) || ! empty($lineItemsErrors)) {
                $errors = [
                    'rejectionArtifacts' => $response['rejectionArtifacts'] ?? [],
                    'lineItemsErrors' => $lineItemsErrors,
                ];
                $suborder->update([
                    'error_code' => VendorError::VENDOR_ERROR,
                    'error_message' => json_encode($errors, JSON_PRETTY_PRINT),
                ]);
                $session->events()->create([
                    'action' => 'order_item_creation_failed',
                    'metadata' => $errors,
                    'subject_type' => SubOrder::class,
                    'subject_id' => $suborder->id,
                    'status' => IntegrationEventStatus::Error,
                ]);
                $session->failed();

                return;
            }

            $session->success();
        } catch (Exception $e) {
            $session->failed();
            throw $e;
        }
    }

    public function syncOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::SyncOrderStatus,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            if ($suborder->externalOrders->isEmpty()) {
                $this->syncExternalOrders($suborder, $session);
                $suborder->refresh();
                if ($suborder->externalOrders->isEmpty()) {
                    Log::warning('No external orders found after sync', [
                        'suborder_id' => $suborder->id,
                        'order_number' => $suborder->order->order_number,
                    ]);
                    $session->failed();

                    return;
                }
            }
            $suborder->externalOrders->each(function ($externalOrder) use ($suborder, $session) {
                $response = $this->client->getOrderByAmazonId(
                    $externalOrder->external_order_id,
                    $session,
                    'sync_order_status',
                    $suborder
                )->data;
                if (! isset($response['orders'][0])) {
                    Log::warning('No order details found in response', [
                        'suborder_id' => $suborder->id,
                        'order_number' => $suborder->order->order_number,
                        'external_order_id' => $externalOrder->external_order_id,
                    ]);

                    return;
                }
                $order = $response['orders'][0];
                $externalOrder->update([
                    'status' => $order['orderStatus'],
                ]);
                $externalStatus = Str::upper($order['orderStatus']);

                $status = match ($externalStatus) {
                    'PENDING APPROVAL', 'PENDING' => OrderItemStatus::Pending,
                    'PAYMENT CONFIRMED' => OrderItemStatus::Accepted,
                    'PENDING FULFILLMENT' => OrderItemStatus::Accepted,
                    'CANCELLED' => OrderItemStatus::Cancelled,
                    'CLOSED' => OrderItemStatus::Delivered,
                    default => (function () use ($externalStatus, $suborder) {
                        Log::warning('Unhandled Amazon order status', [
                            'status' => $externalStatus,
                            'suborder_id' => $suborder->id,
                            'order_number' => $suborder->order->order_number,
                        ]);

                        return $suborder->status;
                    })(),
                };
                foreach ($order['lineItems'] as $lineItem) {
                    OrderItem::query()
                        ->where('id', $lineItem['purchaseOrderLineItem'])
                        ->update([
                            'status' => $status->value,
                            'external_status' => $externalStatus,
                        ]);
                }
            });
            $session->success();
        } catch (Exception $e) {
            $session->failed();
            throw $e;
        }
    }

    public function syncShipment(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::SyncOrderShipments,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            if ($suborder->externalOrders->isEmpty()) {
                $this->syncExternalOrders($suborder, $session);
                $suborder->refresh();
                if ($suborder->externalOrders->isEmpty()) {
                    Log::warning('No external orders found after sync', [
                        'suborder_id' => $suborder->id,
                        'order_number' => $suborder->order->order_number,
                    ]);
                    $session->failed();

                    return;
                }
            }
            $suborder->externalOrders->each(function ($externalOrder) use ($suborder, $session) {
                $response = $this->client->getOrderByAmazonId(
                    $externalOrder->external_order_id,
                    $session,
                    'sync_order_shipments',
                    $suborder
                )->data;

                if (! isset($response['orders'][0])) {
                    Log::warning('No order details found in response', [
                        'suborder_id' => $suborder->id,
                        'order_number' => $suborder->order->order_number,
                        'external_order_id' => $externalOrder->external_order_id,
                    ]);

                    return;
                }

                $order = $response['orders'][0];
                collect($order['shipments'] ?? [])->each(function ($shipment) use ($suborder, $order) {
                    $shipmentModel = $suborder->shipments()->updateOrCreate([
                        'tracking_number' => $shipment['carrierTracking'],
                    ], [
                        'carrier' => $shipment['carrierName'],
                    ]);
                    $itemIds = collect($order['lineItems'])
                        ->filter(fn ($lineItem) => in_array($shipment['carrierTracking'], $lineItem['carrierTrackingNumbers'] ?? [], true))
                        ->map(fn ($lineItem) => $lineItem['purchaseOrderLineItem'])
                        ->filter();
                    $shipmentModel->items()->sync($itemIds);
                });
            });
            $session->success();
        } catch (Exception $e) {
            $session->failed();
            throw $e;
        }
    }

    public function reconcileOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::ReconcileOrderLines,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            if ($suborder->externalOrders->isEmpty()) {
                $this->syncExternalOrders($suborder, $session);
                $suborder->refresh();
                if ($suborder->externalOrders->isEmpty()) {
                    Log::warning('No external orders found after sync', [
                        'suborder_id' => $suborder->id,
                        'order_number' => $suborder->order->order_number,
                    ]);
                    $session->failed();

                    return;
                }
            }
            $suborder->externalOrders->each(function ($externalOrder) use ($suborder, $session) {
                $response = $this->client->getOrderByAmazonId(
                    $externalOrder->external_order_id,
                    $session,
                    'reconcile_order_lines',
                    $suborder
                )->data;

                if (! isset($response['orders'][0])) {
                    Log::warning('No order details found in response', [
                        'suborder_id' => $suborder->id,
                        'order_number' => $suborder->order->order_number,
                        'external_order_id' => $externalOrder->external_order_id,
                    ]);

                    return;
                }
                $order = $response['orders'][0];
                foreach ($order['lineItems'] as $lineItem) {
                    $item = $suborder->items->firstWhere('productOffer.external_id', $lineItem['asin']);
                    if (! $item) {
                        Log::warning('Item not found in suborder', [
                            'suborder_id' => $suborder->id,
                            'sku' => $lineItem['asin'],
                            'external_order_id' => $externalOrder->external_order_id,
                        ]);

                        continue;
                    }
                    $item->update([
                        'price' => (int) round((float) $lineItem['purchasedPricePerUnit']['amount'] * 100),
                        'tax_fee' => (int) round((float) $lineItem['itemTax']['amount'] * 100),
                        'quantity' => (int) $lineItem['itemQuantity'],
                    ]);
                }
            });
            $session->success();
        } catch (Exception $e) {
            $session->failed();
            throw $e;
        }
    }

    public function validateOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        // No validation needed for Amazon orders (for now)
    }

    /**
     * Extract the first OrderIdentifier from a getOrderByExternalId response.
     */
    private function extractOrderIdentifierFromExternalIdResponse(array $externalIdResponse): ?string
    {
        if (! empty($externalIdResponse['lineItems'])) {
            foreach ($externalIdResponse['lineItems'] as $lineItem) {
                foreach ($lineItem['acceptedItems'] ?? [] as $acceptedItem) {
                    foreach ($acceptedItem['artifacts'] ?? [] as $artifact) {
                        if (($artifact['acceptanceArtifactType'] ?? null) === 'OrderIdentifier' && ! empty($artifact['identifier'])) {
                            return $artifact['identifier'];
                        }
                    }
                }
            }
        }

        return null;
    }

    private function syncExternalOrders(SubOrder $suborder, IntegrationSession $session): void
    {
        $externalIdResponse = $this->client->getOrderByExternalId(
            $suborder->order->order_number,
            $suborder->order->user->email,
            $session,
            'sync_external_orders',
            $suborder
        )->data ?? [];

        $orders = [];
        // Use helper to extract OrderIdentifier
        $amazonOrderId = $this->extractOrderIdentifierFromExternalIdResponse($externalIdResponse);
        if ($amazonOrderId) {
            $amazonOrderResponse = $this->client->getOrderByAmazonId(
                $amazonOrderId,
                $session,
                'sync_external_orders_amazon_id',
                $suborder
            )->data;
            $orders = $amazonOrderResponse['orders'] ?? [];
        }

        // Fallback: If no orders found, try getOrdersByDate for the suborder's created_at date
        if (empty($orders)) {
            $createdAt = $suborder->created_at;
            $startDate = $createdAt->toDateString();
            $endDate = $createdAt->copy()->addDay()->toDateString();
            $dateResponse = $this->client->getOrdersByDate(
                $startDate,
                $endDate,
                $session,
                'sync_external_orders_fallback',
                $suborder
            )->data;

            $orders = collect($dateResponse['orders'] ?? [])
                ->filter(fn ($order) => ($order['purchaseOrderNumber'] ?? null) === $suborder->order->order_number)
                ->values()
                ->all();

            if (empty($orders)) {
                Log::warning('No external orders found by externalId/OrderIdentifier or by date fallback', [
                    'suborder_id' => $suborder->id,
                    'order_number' => $suborder->order->order_number,
                    'user_email' => $suborder->order->user->email,
                    'created_at' => $suborder->created_at,
                ]);
            }
        }

        foreach ($orders as $order) {
            $shippingFee = isset($order['orderShippingAndHandling']['amount'])
                ? (int) round(((float) $order['orderShippingAndHandling']['amount']) * 100)
                : null;
            $suborder->externalOrders()->updateOrCreate([
                'external_order_id' => $order['orderId'],
            ], [
                'status' => $order['orderStatus'] ?? 'PENDING',
                'shipping_fee' => $shippingFee,
            ]);
        }
    }

    /**
     * Create the order payload for Amazon's API.
     */
    private function createOrderPayload(SubOrder $suborder): array
    {
        $order = $suborder->order;
        $user = $order->user;

        $attributes = [
            [
                'attributeType' => 'PurchaseOrderNumber',
                'purchaseOrderNumber' => $order->order_number,
            ],
            [
                'attributeType' => 'BuyerReference',
                'userReference' => [
                    'userReferenceType' => 'UserEmail',
                    'emailAddress' => $user->email,
                ],
            ],
            [
                'attributeType' => 'Region',
                'region' => 'US',
            ],
            [
                'attributeType' => 'ShippingAddress',
                'address' => [
                    'addressType' => 'PhysicalAddress',
                    'fullName' => $user->name,
                    'addressLine1' => $order->shippingAddress->street,
                    'city' => $order->shippingAddress->city,
                    'stateOrRegion' => $order->shippingAddress->state,
                    'postalCode' => $order->shippingAddress->postal_code,
                    'countryCode' => 'US',
                ],
            ],
            [
                'attributeType' => 'BuyingGroupReference',
                'groupReference' => [
                    'groupReferenceType' => 'GroupIdentity',
                    'identifier' => $this->buyingGroupId,
                ],
            ],
            [
                'attributeType' => 'SelectedPaymentMethodReference',
                'paymentMethodReference' => [
                    'paymentMethodReferenceType' => 'StoredPaymentMethod',
                ],
            ],
        ];

        // Add TrialMode attribute for non-production environments
        if (app()->environment() !== 'production') {
            $attributes[] = [
                'attributeType' => 'TrialMode',
            ];
        }

        return [
            'externalId' => $order->order_number,
            'lineItems' => $suborder->items->map(fn (OrderItem $item) => [
                'externalId' => (string) $item->id,
                'quantity' => $item->quantity,
                'attributes' => [
                    [
                        'attributeType' => 'SelectedProductReference',
                        'productReference' => [
                            'productReferenceType' => 'ProductIdentifier',
                            'id' => $item->productOffer->external_id,
                        ],
                    ],
                    [
                        'attributeType' => 'SelectedBuyingOptionReference',
                        'buyingOptionReference' => [
                            'buyingOptionReferenceType' => 'BuyingOptionIdentifier',
                            'id' => 'OfferID',
                        ],
                    ],
                ],
                'expectations' => [
                    [
                        'expectationType' => 'ExpectedUnitPrice',
                        'amount' => [
                            'currencyCode' => 'USD',
                            'amount' => $item->price,
                        ],
                    ],
                    [
                        'expectationType' => 'ExpectedCharge',
                        'amount' => [
                            'currencyCode' => 'USD',
                            'amount' => $item->total_price,
                        ],
                        'source' => 'SUBTOTAL',
                    ],
                ],
            ])->all(),
            'attributes' => $attributes,
        ];
    }
}
