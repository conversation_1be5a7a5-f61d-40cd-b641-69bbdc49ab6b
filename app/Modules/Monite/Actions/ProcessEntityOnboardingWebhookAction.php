<?php

declare(strict_types=1);

namespace App\Modules\Monite\Actions;

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Models\MoniteEntity;
use App\Modules\Monite\Models\MoniteWebhookEvent;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use DateTimeImmutable;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;

final class ProcessEntityOnboardingWebhookAction
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteApiClientInterface $apiClient
    ) {}

    /**
     * Process an entity onboarding webhook event
     */
    public function execute(MoniteWebhookEvent $webhookEvent, Clinic $clinic): void
    {
        // Check if Monite is enabled for this clinic
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'processEntityOnboardingWebhook')) {
            $webhookEvent->markAsIgnored('Monite feature disabled for this clinic');

            return;
        }

        try {
            $webhookEvent->markAsProcessing();

            // Fetch the latest entity details from Monite
            $entityDetails = $this->fetchEntityDetails($clinic->monite_entity_id);

            // Fetch onboarding requirements
            $onboardingRequirements = $this->fetchOnboardingRequirements($clinic->monite_entity_id);

            // Create or update MoniteEntity record with full entity data
            $this->createOrUpdateMoniteEntity($clinic, $entityDetails, $onboardingRequirements);

            Log::info('Entity onboarding webhook processed', [
                'event_id' => $webhookEvent->event_id,
                'event_type' => $webhookEvent->event_type,
                'clinic_id' => $clinic->id,
                'entity_id' => $clinic->monite_entity_id,
                'entity_status' => $entityDetails['status'] ?? null,
            ]);

            $webhookEvent->markAsProcessed();
        } catch (Throwable $e) {
            Log::error('Error processing entity onboarding webhook', [
                'event_id' => $webhookEvent->event_id,
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $webhookEvent->markAsFailed("Error: {$e->getMessage()}");
            throw $e;
        }
    }

    /**
     * Fetch complete entity details from Monite API
     */
    private function fetchEntityDetails(string $entityId): array
    {
        $response = $this->apiClient
            ->withEntityId($entityId)
            ->get('/entities/me');

        return $response->json();
    }

    /**
     * Fetch onboarding requirements from Monite API
     */
    private function fetchOnboardingRequirements(string $entityId): ?array
    {
        try {
            $response = $this->apiClient
                ->withEntityId($entityId)
                ->get('/onboarding_requirements');

            if ($response->successful()) {
                $responseData = $response->json();

                // Return the first object from data array, not the array
                return $responseData['data'][0] ?? null;
            }

            Log::warning('Failed to fetch onboarding requirements', [
                'entity_id' => $entityId,
                'status' => $response->status(),
            ]);

            return null;
        } catch (Exception $e) {
            Log::warning('Exception fetching onboarding requirements', [
                'entity_id' => $entityId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Create or update MoniteEntity record with full entity data
     */
    private function createOrUpdateMoniteEntity(Clinic $clinic, array $entityDetails, ?array $onboardingRequirements = null): void
    {
        $moniteEntity = MoniteEntity::updateOrCreate(
            [
                'monite_entity_id' => $clinic->monite_entity_id,
            ],
            [
                'clinic_id' => $clinic->id,
                'entity_data' => $entityDetails,
                'onboarding_requirements' => $onboardingRequirements,
                'status' => $entityDetails['status'] ?? null,
                'type' => $entityDetails['type'] ?? null,
                'email' => $entityDetails['email'] ?? null,
                'monite_created_at' => isset($entityDetails['created_at']) ? new DateTimeImmutable($entityDetails['created_at']) : null,
                'monite_updated_at' => isset($entityDetails['updated_at']) ? new DateTimeImmutable($entityDetails['updated_at']) : null,
            ]
        );

        Log::debug('MoniteEntity record updated', [
            'clinic_id' => $clinic->id,
            'monite_entity_id' => $moniteEntity->monite_entity_id,
            'status' => $moniteEntity->status,
            'type' => $moniteEntity->type,
            'has_onboarding_requirements' => $onboardingRequirements !== null,
        ]);
    }
}
