<?php

declare(strict_types=1);

namespace App\Modules\Monite\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Clinic;
use App\Modules\Monite\Actions\ProcessCounterpartWebhookAction;
use App\Modules\Monite\Actions\ProcessEntityOnboardingWebhookAction;
use App\Modules\Monite\Actions\ProcessPayableWebhookAction;
use App\Modules\Monite\Models\MoniteWebhookEvent;
use App\Modules\Monite\Services\MoniteWebhookVerificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class MoniteWebhookController extends Controller
{
    /**
     * Supported webhook event types
     */
    private const SUPPORTED_EVENTS = [
        'payable.created',
        'payable.updated',
        'payable.ocr_finished',
        'counterpart.created',
        'entity.onboarding_requirements.status_updated',
        'entity.updated',
        'entity.onboarding_requirements.updated',
    ];

    public function __construct(
        private readonly MoniteWebhookVerificationService $verificationService,
        private readonly ProcessPayableWebhookAction $processPayableAction,
        private readonly ProcessCounterpartWebhookAction $processCounterpartAction,
        private readonly ProcessEntityOnboardingWebhookAction $processEntityOnboardingAction
    ) {}

    /**
     * Handle incoming webhook events from Monite
     */
    public function handle(Request $request): JsonResponse
    {
        $payload = $request->all();
        $signature = $request->header('monite-signature');

        // Log the incoming webhook for debugging purposes
        Log::info('Received Monite webhook', [
            'event_id' => $payload['id'] ?? null,
            'action' => $payload['action'] ?? null,
            'event_type' => $payload['object_type'] ?? null,
        ]);

        // Verify webhook signature
        $objectType = $payload['object_type'] ?? null;
        if (! $this->verificationService->verifySignature($signature, $request->getContent(), $objectType)) {
            Log::warning('Invalid Monite webhook signature', [
                'signature' => $signature,
                'object_type' => $objectType,
            ]);

            return response()->json([
                'error' => 'Invalid signature',
                'code' => 'INVALID_SIGNATURE',
                'timestamp' => now()->toISOString(),
            ], 401);
        }

        // Basic validation of the webhook payload
        if (empty($payload['id']) || empty($payload['action']) || empty($payload['entity_id'])) {
            Log::warning('Invalid Monite webhook payload', [
                'payload' => $payload,
            ]);

            return response()->json([
                'error' => 'Invalid payload',
                'code' => 'INVALID_PAYLOAD',
                'timestamp' => now()->toISOString(),
            ], 400);
        }

        // Check for duplicate events (idempotency)
        $existingEvent = MoniteWebhookEvent::where('event_id', $payload['id'])->first();
        if ($existingEvent) {
            Log::info('Duplicate Monite webhook event, ignoring', [
                'event_id' => $payload['id'],
            ]);

            return response()->json([
                'status' => 'already processed',
                'timestamp' => now()->toISOString(),
            ], 200);
        }

        // Wrap the entire operation in a database transaction
        return DB::transaction(function () use ($payload) {
            // Store the webhook event
            $webhookEvent = MoniteWebhookEvent::create([
                'event_id' => $payload['id'],
                'event_type' => $payload['action'],
                'entity_id' => $payload['entity_id'],
                'object_type' => $payload['object_type'] ?? null,
                'object_id' => $payload['object_id'] ?? null,
                'payload' => $payload,
            ]);

            try {
                // Find the clinic for this entity
                $clinic = Clinic::where('monite_entity_id', $payload['entity_id'])->first();

                if (! $clinic) {
                    Log::warning('No clinic found for Monite entity', [
                        'entity_id' => $payload['entity_id'],
                    ]);

                    return response()->json([
                        'error' => 'No clinic found for entity',
                        'code' => 'CLINIC_NOT_FOUND',
                        'timestamp' => now()->toISOString(),
                    ], 404);
                }

                // Process webhook events
                if (in_array($payload['action'], self::SUPPORTED_EVENTS)) {
                    if (str_starts_with($payload['action'], 'payable.')) {
                        $this->processPayableAction->execute($webhookEvent, $clinic);
                    } elseif (str_starts_with($payload['action'], 'counterpart.')) {
                        $this->processCounterpartAction->execute($webhookEvent, $clinic);
                    } elseif (str_starts_with($payload['action'], 'entity.')) {
                        $this->processEntityOnboardingAction->execute($webhookEvent, $clinic);
                    }
                } else {
                    Log::info('Unhandled Monite webhook event type', [
                        'event_type' => $payload['action'],
                    ]);
                }

                return response()->json([
                    'status' => 'processed',
                    'timestamp' => now()->toISOString(),
                ], 200);
            } catch (Throwable $e) {
                Log::error('Error processing Monite webhook', [
                    'event_id' => $payload['id'],
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                // Mark webhook event as failed
                $webhookEvent->markAsFailed("Processing error: {$e->getMessage()}");

                return response()->json([
                    'error' => 'Internal server error',
                    'code' => 'PROCESSING_ERROR',
                    'timestamp' => now()->toISOString(),
                ], 500);
            }
        });
    }
}
