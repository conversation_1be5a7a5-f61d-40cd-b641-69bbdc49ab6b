<?php

declare(strict_types=1);

namespace App\Modules\Monite\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MoniteOnboardingStatusController extends Controller
{
    public function __construct(
        private readonly MoniteFeatureFlagService $featureFlagService
    ) {}

    /**
     * Get the Monite onboarding status for the current clinic
     */
    public function show(Request $request): JsonResponse
    {
        $clinic = $request->clinic();

        // Check if Monite integration is enabled for this clinic
        if (! $this->featureFlagService->isEnabled($clinic)) {
            return response()->json([
                'error' => 'Monite integration is not enabled for this clinic',
                'code' => 'MONITE_NOT_ENABLED',
            ], 403);
        }

        // Check if the clinic has a Monite entity
        if (! $clinic->monite_entity_id) {
            return response()->json([
                'error' => 'No Monite entity found for this clinic',
                'code' => 'MONITE_ENTITY_NOT_FOUND',
            ], 404);
        }

        // Get data from MoniteEntity relationship
        $moniteEntity = $clinic->moniteEntity;

        if (! $moniteEntity) {
            return response()->json([
                'error' => 'Monite entity data not synchronized yet',
                'code' => 'MONITE_ENTITY_NOT_SYNCED',
            ], 404);
        }

        // Get verification status from onboarding requirements
        $verificationStatus = $moniteEntity->onboarding_requirements['verification_status'] ?? null;

        return response()->json([
            'entity_id' => $clinic->monite_entity_id,
            'entity_status' => $moniteEntity->status,
            'verification_status' => $verificationStatus,
            'onboarding_requirements' => $moniteEntity->onboarding_requirements,
            'last_synced_at' => $moniteEntity->updated_at?->toISOString(),
            'is_onboarding_complete' => $moniteEntity->isOnboardingComplete(),
        ]);
    }
}
