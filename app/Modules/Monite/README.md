# Monite Integration Module

![Monite Logo](https://docs.monite.com/img/logo.svg)

## 📋 Overview

This module provides complete integration with [Monite's](https://monite.com/) accounts payable platform, enabling clinics to manage payables, vendors, and payment workflows through a comprehensive API and webhook system.

### Core Features

- 🏢 **Entity Management**: Create and sync clinic entities with Monite
- 👥 **User & Role Sync**: Automatic user/role synchronization
- 📄 **Payable Processing**: AI-powered invoice processing and OCR
- 🔗 **Webhook Integration**: Real-time data synchronization
- 💳 **Payment Methods**: Automated payment method setup
- 🎯 **Feature Flags**: Per-clinic integration control
- 📧 **Email Integration**: Direct invoice receipt via mailboxes
- 🔒 **Security**: Comprehensive authentication and authorization

## 🚀 Installation & Setup

### Environment Configuration

```bash
# Monite API Configuration
MONITE_BASE_URL="https://api.sandbox.monite.com"
MONITE_CLIENT_ID="your_client_id"
MONITE_CLIENT_SECRET="your_client_secret"

# Webhook Secrets (generate via Monite CLI)
MONITE_WEBHOOK_SECRET_PAYABLE="your_payable_webhook_secret"
MONITE_WEBHOOK_SECRET_COUNTERPART="your_counterpart_webhook_secret"
MONITE_WEBHOOK_SECRET_ENTITY="your_entity_webhook_secret"

# Mailbox Configuration (for email-to-invoice processing)
MONITE_MAILBOX_DOMAIN_ID="0f98d790-6fef-42c3-a00e-0608d68f81c5"
```

### Database Migration

Run the migration to create the `MoniteEntity` table:

```bash
php artisan migrate
```

This creates the `monite_entities` table with complete entity data storage.

### Feature Flag Setup

Enable Monite for specific clinics using Laravel Pennant:

```php
// Enable for a clinic
Feature::activate('monite-integration', $clinic);

// Check if enabled
if (Feature::active('monite-integration', $clinic)) {
    // Monite operations
}
```

### Configuration Files

The module uses `config/monite.php` for all configuration:

```php
return [
    'base_url' => env('MONITE_BASE_URL', 'https://api.sandbox.monite.com'),
    'client_id' => env('MONITE_CLIENT_ID'),
    'client_secret' => env('MONITE_CLIENT_SECRET'),
    
    'webhook_secrets' => [
        'payable' => env('MONITE_WEBHOOK_SECRET_PAYABLE'),
        'counterpart' => env('MONITE_WEBHOOK_SECRET_COUNTERPART'),
        'entity' => env('MONITE_WEBHOOK_SECRET_ENTITY'),
    ],
    
    'payment_methods' => [
        'receive' => [
            'card',   // Required by Monite
            'us_ach', // US ACH payments
        ],
        'send' => [
            // EU/UK vendor payments
        ],
    ],
    
    'default_roles' => [
        'owner' => ['entity', 'read_write'],
        'admin' => ['entity', 'read_write'],
        'member' => ['entity', 'read'],
    ],
];
```

## 🏗️ Architecture

### Service Layer

#### `MoniteApiClientInterface`
HTTP client for Monite API with automatic authentication:
```php
$client = app(MoniteApiClientInterface::class);
$response = $client->withEntityId($entityId)->get('/payables');
```

#### `MoniteEntityService`
Entity lifecycle management:
```php
$entityService = app(MoniteEntityService::class);
$entityId = $entityService->createEntityForClinic($clinic);
$entityService->updateEntityForClinic($clinic);
```

#### `MoniteUserService`
User and role synchronization:
```php
$userService = app(MoniteUserService::class);
$userService->syncUserForClinic($user, $clinic);
$userService->syncRolesForClinic($clinic);
```

#### `MonitePayableService`
Invoice and payable processing:
```php
$payableService = app(MonitePayableService::class);
$count = $payableService->processClinicInvoices($clinic);
```

#### `MoniteWebhookVerificationService`
Webhook signature verification:
```php
$verificationService = app(MoniteWebhookVerificationService::class);
$isValid = $verificationService->verifySignature($payload, $signature, $objectType);
```

#### `MoniteFeatureFlagService`
Feature flag management:
```php
$featureFlagService = app(MoniteFeatureFlagService::class);
$isEnabled = $featureFlagService->isEnabled($clinic);
```

### `MoniteTokenManager`
OAuth2 token management with caching:
```php
$tokenManager = app(MoniteTokenManager::class);
$token = $tokenManager->getAccessToken();
```

### `MoniteCli`
Comprehensive CLI tool for managing all Monite operations:
```bash
# Show overall status
php artisan monite:cli status

# Manage webhooks
php artisan monite:cli list-webhooks
php artisan monite:cli register-webhook --webhook-url=https://example.com/webhook --object-type=payable

# Manage settings
php artisan monite:cli update-settings --clinic-id=123 --setting=allow_cancel_duplicates_automatically --value=true

# Manage domains
php artisan monite:cli list-domains
php artisan monite:cli create-domain --domain=example.com

# Manage mailboxes
php artisan monite:cli list-mailboxes --clinic-id=123
php artisan monite:cli create-mailbox --clinic-id=123 --mailbox-name=payables
```

## 🗄️ Models

### `MoniteUserMapping`
Maps local users to Monite users:
```php
// Fields: clinic_id, user_id, monite_user_id
$mapping = MoniteUserMapping::where('clinic_id', $clinic->id)
    ->where('user_id', $user->id)
    ->first();
```

### `MoniteRoleMapping`
Maps local roles to Monite roles:
```php
// Fields: clinic_id, role_id, monite_role_id
$mapping = MoniteRoleMapping::where('clinic_id', $clinic->id)
    ->where('role_id', $role->id)
    ->first();
```

### `MoniteInvoice`
Local storage of processed invoices:
```php
// Fields: clinic_id, monite_payable_id, filename, file_url, status, amount_cents, currency_code, vendor_name, due_date, extracted_data
$invoice = MoniteInvoice::where('clinic_id', $clinic->id)
    ->where('status', 'approved')
    ->get();
```

### `MoniteWebhookEvent`
Webhook event tracking and processing:
```php
// Fields: event_id, event_type, object_type, object_id, clinic_id, payload, status, processed_at, failed_at, error_message
$events = MoniteWebhookEvent::where('clinic_id', $clinic->id)
    ->where('status', 'processed')
    ->get();
```

### `MoniteEntity`
Complete Monite entity data storage:
```php
// Primary data store for all entity information
$entity = $clinic->moniteEntity;
$isComplete = $entity->isOnboardingComplete();
$requirements = $entity->onboarding_requirements;
```

## 🎮 Controllers

### `MoniteWebhookController`
Handles incoming webhooks from Monite:
```php
// Supported events:
- payable.created, payable.updated, payable.ocr_finished
- counterpart.created  
- entity.updated, entity.onboarding_requirements.status_updated, entity.onboarding_requirements.updated
```

### `MoniteOnboardingStatusController`
API endpoint for frontend onboarding status:
```php
GET /api/monite/onboarding-status
// Returns entity status, requirements, and completion status
```

### `MoniteEntityUserTokenController`
Generates user tokens for Monite SDK:
```php
POST /api/monite/entity-user-token
// Returns token for frontend Monite component authentication
```

## 🚀 Actions & Jobs

### Actions (Synchronous)

#### `CreateOrUpdateEntityAction`
Creates or updates Monite entity:
```php
$action = app(CreateOrUpdateEntityAction::class);
$entityId = $action->execute($clinic);
```

#### `ProcessPayableWebhookAction`
Processes payable-related webhooks:
```php
$action = app(ProcessPayableWebhookAction::class);
$action->execute($webhookEvent, $clinic);
```

#### `ProcessCounterpartWebhookAction`
Processes counterpart-related webhooks:
```php
$action = app(ProcessCounterpartWebhookAction::class);
$action->execute($webhookEvent, $clinic);
```

#### `ProcessEntityOnboardingWebhookAction`
Processes entity onboarding webhooks:
```php
$action = app(ProcessEntityOnboardingWebhookAction::class);
$action->execute($webhookEvent, $clinic);
```

### Jobs (Asynchronous)

#### `CreateOrUpdateMoniteEntityJob`
Queued entity creation/update:
```php
CreateOrUpdateMoniteEntityJob::dispatch($clinic);
```

#### `SyncMissingMoniteEntitiesJob`
Bulk sync for missing entities:
```php
SyncMissingMoniteEntitiesJob::dispatch($clinic);
```

#### `SyncMoniteUsersJob`
Bulk user synchronization:
```php
SyncMoniteUsersJob::dispatch($clinic);
```

## 📡 Webhook Management

### Registering Webhooks
```bash
# Register all webhook types
php artisan monite:cli register-webhook --webhook-url=https://yourdomain.com/api/webhooks/monite --object-type=payable
php artisan monite:cli register-webhook --webhook-url=https://yourdomain.com/api/webhooks/monite --object-type=counterpart
php artisan monite:cli register-webhook --webhook-url=https://yourdomain.com/api/webhooks/monite --object-type=entity

# List all webhooks
php artisan monite:cli list-webhooks

# Delete webhooks
php artisan monite:cli delete-webhook --webhook-id=123
```

### Webhook Event Types
- **Payable Events**: `payable.created`, `payable.updated`, `payable.ocr_finished`
- **Counterpart Events**: `counterpart.created`
- **Entity Events**: `entity.updated`, `entity.onboarding_requirements.status_updated`, `entity.onboarding_requirements.updated`

### Webhook Processing Flow
1. **Verification**: Signature validation using webhook secrets
2. **Deduplication**: Event ID-based duplicate prevention
3. **Processing**: Dispatch to appropriate action handlers
4. **Logging**: Complete audit trail of all events

## 📧 Email Integration

### Domain Management
```bash
# List domains
php artisan monite:cli list-domains

# Create domain
php artisan monite:cli create-domain --domain=mail.example.com

# Verify domain (after DNS setup)
php artisan monite:cli verify-domain --domain-id=domain_123
```

### Mailbox Management
```bash
# List mailboxes
php artisan monite:cli list-mailboxes --clinic-id=123

# Create payable mailbox
php artisan monite:cli create-mailbox --clinic-id=123 --mailbox-name=payables --related-object-type=payable

# Create receipt mailbox  
php artisan monite:cli create-mailbox --clinic-id=123 --mailbox-name=receipts --related-object-type=receipt

# Delete mailbox
php artisan monite:cli delete-mailbox --clinic-id=123 --mailbox-id=mailbox_123
```

### Testing Commands
```bash
# Dry run (preview only)
php artisan monite:cli create-mailbox --clinic-id=123 --mailbox-name=test --dry-run
```

## 🚀 Usage Examples

### Manual Entity Creation
```php
$entityService = app(MoniteEntityService::class);
$entityId = $entityService->createEntityForClinic($clinic);
// Automatically:
// 1. Creates entity in Monite
// 2. Syncs complete entity details to MoniteEntity table
// 3. Fetches and stores onboarding requirements
// 4. Enables payment methods (card + us_ach)
```

### Manual Entity Update
```php
$entityService = app(MoniteEntityService::class);
$entityService->updateEntityForClinic($clinic);
// Automatically:
// 1. Updates entity in Monite
// 2. Syncs updated entity details to MoniteEntity table
// 3. Re-enables/updates payment methods
// 4. Ensures mailbox exists for entity (clinic-name-payables)
```

### Create or Update Entity (via Action/Job)
```php
// Via Action
$action = app(CreateOrUpdateEntityAction::class);
$entityId = $action->execute($clinic);
// Automatically creates/updates entity AND syncs to MoniteEntity table
// Also enables payment methods and creates mailbox (clinic-name-payables) during BOTH creation AND updates

// Via Job (queued)
CreateOrUpdateMoniteEntityJob::dispatch($clinic);
// Same as action, but runs in background
```

### Payment Methods Configuration
Payment methods are automatically enabled during **both entity creation AND updates** based on the configuration in `config/monite.php`:

```php
'payment_methods' => [
    'receive' => [
        'card',   // Card payments (required by Monite, includes Apple Pay, Google Pay)
        'us_ach', // US ACH payments for receiving money from customers
        // Add more: 'sepa_credit', 'sepa_debit', etc.
    ],
    'send' => [
        // Payment methods for sending money to vendors (EU/UK only)
        // 'sepa_credit' - for making payments to vendors
    ],
],
```

**Important Notes:**
- `card` payment method is **required** by Monite and must be included
- Payment methods are enabled during **both** entity creation and updates
- If payment methods fail to enable, it won't block entity operations (graceful degradation)

Supported payment methods include:
- `card` - Card payments (includes Apple Pay, Google Pay) - **Required**
- `us_ach` - US ACH payments
- `sepa_credit` - SEPA credit transfers (EU)
- `sepa_debit` - SEPA direct debit (EU)
- `klarna` - Klarna payments
- And more based on [country availability](https://docs.monite.com/v-2024-05-25/api/payment-methods/put-entities-id-payment-methods)

### Check Onboarding Status
```php
// Via API endpoint (recommended for frontend)
GET /api/monite/onboarding-status
Header: Highfive-Clinic: {clinic_id}

// Response format (camelCase):
{
    "entityId": "string",
    "onboardingStatus": "active",
    "onboardingRequirements": {
        "requirements": {
            "currentlyDue": [],
            "eventuallyDue": [],
            "currentDeadline": null,
            "pendingVerification": []
        },
        "disabledReason": null,
        "requirementsErrors": [],
        "verificationErrors": [],
        "verificationStatus": "enabled"
    },
    "lastSyncedAt": "2025-09-30T13:28:11.000000Z",
    "isOnboardingComplete": true
}

// Via model relationship
$clinic = Clinic::find($clinicId);
$moniteEntity = $clinic->moniteEntity;
$status = $moniteEntity->status;
$requirements = $moniteEntity->onboarding_requirements;
$isComplete = $moniteEntity->isOnboardingComplete();
```

// Via Model
$entity = $clinic->moniteEntity;
if ($entity && $entity->isOnboardingComplete()) {
    // Onboarding complete
}

// Check specific requirements
$requirements = $entity->onboarding_requirements;
$needsAction = !empty($requirements['requirements']['currently_due']);
```

### User Sync
```php
$userService = app(MoniteUserService::class);
$userService->syncUserForClinic($user, $clinic);
```

### Payable Processing
```php
$payableService = app(MonitePayableService::class);
$count = $payableService->processClinicInvoices($clinic);
```

### Queue Jobs
```php
// Create/update entity
CreateOrUpdateMoniteEntityJob::dispatch($clinic);

// Dispatch sync job
SyncMissingMoniteEntitiesJob::dispatch($clinic);

// Dispatch user sync
SyncMoniteUsersJob::dispatch($clinic);
```

### Domain and Mailbox Management
```bash
# List all domains
php artisan monite:cli list-domains

# Create a custom domain
php artisan monite:cli create-domain --domain=mail.example.com

# Verify domain (after setting up DNS records)
php artisan monite:cli verify-domain --domain-id=domain_id

# List mailboxes for a clinic
php artisan monite:cli list-mailboxes --clinic-id=123

# Create a mailbox for payables
php artisan monite:cli create-mailbox --clinic-id=123 --mailbox-name=payables --related-object-type=payable

# Create a mailbox for receipts
php artisan monite:cli create-mailbox --clinic-id=123 --mailbox-name=receipts --related-object-type=receipt

# Delete a mailbox
php artisan monite:cli delete-mailbox --clinic-id=123 --mailbox-id=mailbox_id
```

### Webhook Testing
```php
// Test webhook processing
$webhookController = app(MoniteWebhookController::class);
$request = Request::create('/api/webhooks/monite', 'POST', $payload, [], [], [
    'HTTP_MONITE_SIGNATURE' => $signature
]);
$response = $webhookController->handle($request);

// Check webhook events
$events = MoniteWebhookEvent::where('event_type', 'payable.created')->get();
$failedEvents = MoniteWebhookEvent::failed()->get();

// Check invoice data
$invoices = MoniteInvoice::where('clinic_id', $clinic->id)->get();
$invoice = MoniteInvoice::where('monite_payable_id', $payableId)->first();
```

## 📈 Performance Features

- **Token Caching**: OAuth2 tokens cached for 1 hour
- **Batch Processing**: Bulk user and entity synchronization
- **Queue Jobs**: Async processing for heavy operations
- **Webhook Deduplication**: Event ID-based duplicate prevention
- **Database Optimization**: Indexed queries and relationship eager loading
- **Error Recovery**: Automatic retry mechanisms for failed operations

## Complete Onboarding Workflow

1. **Entity Creation**: When a clinic is created or updated, a Monite entity is automatically created/updated
2. **Entity Data Sync**: Complete entity details and onboarding requirements are fetched and stored locally
3. **Payment Methods**: Card and US ACH payment methods are automatically enabled
4. **Mailbox Creation**: Email-to-invoice mailbox is created for the entity (e.g., `<EMAIL>`)
5. **Settings Configuration**: Entity settings are configured based on config values
6. **Role Management**: Default roles are created for clinic users
7. **User Synchronization**: Clinic users are synced to Monite
8. **Webhook Integration**: Real-time updates via webhooks keep data in sync
9. **Feature Flags**: Monite integration can be enabled/disabled per clinic

### Logs to Monitor
Monitor these log entries for successful onboarding:
```
- "Monite entity created for clinic"
- "Entity details synced to local database"
- "Enabling payment methods for Monite entity"
- "Payment methods enabled successfully"
- "Creating mailbox for Monite entity"
- "Mailbox created successfully"
- "Mailbox address saved to MoniteEntity"
- "Existing mailbox address saved to MoniteEntity" (when mailbox already exists)
```

## Frontend Integration

The React/TypeScript frontend integrates with Monite via the backend API. Key components include:

### API Endpoints
- `GET /api/monite/onboarding-status` - Get onboarding status for current clinic
- `POST /api/monite/entity-user-token` - Generate user token for Monite SDK
- `POST /api/feature-flags/check-monite` - Check if Monite is enabled for clinic

### Frontend Integration Hooks

**React Query Hook:**
```typescript
// src/libs/monite/hooks/useMoniteOnboardingStatus.ts
const { isOnboardingComplete, onboardingStatus, isLoading } = 
  useIsMoniteOnboardingComplete(clinicId);
```

**API Response Interface (camelCase):**
```typescript
interface MoniteOnboardingStatusResponse {
  entityId: string;
  onboardingStatus: string;
  onboardingRequirements: object | null;
  lastSyncedAt: string | null;
  isOnboardingComplete: boolean;
}
```

### Frontend Components
Frontend uses conditional rendering based on onboarding status:
```tsx
// src/apps/shop/pages/Invoices/Invoices.tsx
{!isOnboardingComplete ? (
  <Onboarding />  // Show onboarding flow
) : (
  <Payables />    // Show payables management
)}
```

**Cache Management:**
- `staleTime: 2 minutes` - Data considered fresh for 2 minutes
- `refetchOnWindowFocus: true` - Refetch when tab regains focus
- Manual refetch available via `refetch()` function

## Database Schema

### Clinic Table
- `monite_entity_id` - UUID of the Monite entity (foreign key reference)

### MoniteEntity Table (Primary Data Store)
- `id` - UUID primary key
- `monite_entity_id` - String, unique Monite entity identifier
- `clinic_id` - Foreign key to clinics table
- `entity_data` - JSONB, complete Monite entity object
- `onboarding_requirements` - JSONB, current onboarding requirements
- `status` - String, current entity status (active, pending, etc.)
- `type` - String, entity type (organization, individual)
- `email` - String, entity email
- `mailbox_address` - String, full mailbox email address for invoice processing
- `monite_created_at` - Timestamp when entity was created in Monite
- `monite_updated_at` - Timestamp when entity was last updated in Monite
- `created_at` / `updated_at` - Local timestamps

### Relationship
```php
// Clinic model
public function moniteEntity(): HasOne
{
    return $this->hasOne(MoniteEntity::class);
}

// MoniteEntity model  
public function clinic(): BelongsTo
{
    return $this->belongsTo(Clinic::class);
}
```

### MoniteWebhookEvent Table
- `id` - UUID primary key
- `event_id` - Unique Monite event identifier  
- `event_type` - Type of webhook event
- `object_type` - Monite object type (payable, counterpart, entity)
- `object_id` - Monite object identifier
- `clinic_id` - Associated clinic
- `payload` - Complete webhook payload (JSONB)
- `status` - Processing status (pending, processing, processed, failed, ignored)
- `processed_at` - Processing completion timestamp
- `failed_at` - Processing failure timestamp
- `error_message` - Error details for failed processing

### MoniteInvoice Table
- `id` - UUID primary key
- `clinic_id` - Associated clinic
- `monite_payable_id` - Monite payable identifier
- `filename` - Original filename
- `file_url` - Monite file URL
- `status` - Processing status
- `amount_cents` - Invoice amount in cents
- `currency_code` - Currency (USD, EUR, etc.)
- `vendor_name` - Extracted vendor name
- `due_date` - Payment due date
- `extracted_data` - Complete OCR extracted data (JSONB)

### MoniteUserMapping Table
- `id` - UUID primary key
- `clinic_id` - Associated clinic
- `user_id` - Local user ID
- `monite_user_id` - Monite user identifier

### MoniteRoleMapping Table
- `id` - UUID primary key
- `clinic_id` - Associated clinic
- `role_id` - Local role ID
- `monite_role_id` - Monite role identifier

## 🔧 CLI Commands

### Status & Information
```bash
# Show overall module status
php artisan monite:cli status

# List all available commands
php artisan monite:cli --help
```

### Webhook Management
```bash
# List webhooks
php artisan monite:cli list-webhooks

# Register webhook
php artisan monite:cli register-webhook --webhook-url=https://example.com/webhook --object-type=payable

# Delete webhook
php artisan monite:cli delete-webhook --webhook-id=123

# Delete all webhooks
php artisan monite:cli delete-all-webhooks
```

### Settings Management
```bash
# Update entity settings
php artisan monite:cli update-settings --clinic-id=123 --setting=allow_cancel_duplicates_automatically --value=true
```

### Domain & Mailbox Management
```bash
# Domain operations
php artisan monite:cli list-domains
php artisan monite:cli create-domain --domain=mail.example.com
php artisan monite:cli verify-domain --domain-id=domain_123

# Mailbox operations
php artisan monite:cli list-mailboxes --clinic-id=123
php artisan monite:cli create-mailbox --clinic-id=123 --mailbox-name=payables
php artisan monite:cli delete-mailbox --clinic-id=123 --mailbox-id=mailbox_123
```

## 🛡️ Security

### Authentication
- **OAuth2 Flow**: Secure token-based API authentication
- **Token Caching**: Secure token storage with expiration
- **Entity Context**: All operations scoped to clinic entities

### Webhook Security
- **Signature Verification**: HMAC-SHA256 signature validation
- **Secret Management**: Environment-based secret configuration
- **Request Validation**: Comprehensive payload validation

### Authorization
- **Role-Based Access**: Granular permission management
- **Feature Flags**: Clinic-level integration control
- **Audit Logging**: Complete operation audit trail

## 📊 Monitoring & Logging

### Log Categories
- **API Requests**: HTTP client operations
- **Webhook Events**: Incoming webhook processing
- **Entity Operations**: CRUD operations
- **Sync Jobs**: Background job execution
- **Errors**: Exception tracking and reporting

### Monitoring Features
- **Job Status**: Queue job monitoring
- **Webhook Status**: Real-time event processing
- **Entity Status**: Onboarding progress tracking
- **Performance Metrics**: Operation timing and success rates

### Debugging Features
- **Request/Response Logging**: Detailed API communication
- **Error Context**: Rich error information with context
- **Comprehensive Logging**: All operations logged
- **Error Tracking**: Detailed error information
- **Performance Metrics**: Request timing and counts
- **Status Reporting**: Command status summaries

## 📍 API Endpoints

All Monite API endpoints are defined in `routes/api.php`:

### Public Endpoints
- `POST /api/webhooks/monite` - Webhook handler (no auth, signature verification)

### Protected Endpoints (require `auth:sanctum` + `header:Highfive-Clinic`)
- `POST /api/monite/entity-user-token` - Generate entity user token
- `GET /api/monite/onboarding-status` - Get onboarding status
- `POST /api/feature-flags/check-monite` - Check Monite feature flag

All protected endpoints require:
1. **Authentication**: Valid Sanctum token
2. **Clinic Context**: `Highfive-Clinic` header with clinic ID

## Troubleshooting

### Common Issues

**Entity Creation Fails**
- Check Monite feature flag is enabled for clinic
- Verify API credentials in environment variables
- Check required clinic fields (name, tax_id, address)
- Monitor logs for "Monite entity created for clinic"

**Payment Methods Fail**
- Error: "Payment method 'card' must be requested" → Ensure 'card' is included in config
- Monitor logs for "Payment methods enabled successfully"
- Payment method failures won't block entity operations (graceful degradation)

**Frontend Shows Wrong Component**
- Check API response format matches TypeScript interface (camelCase)
- Clear React Query cache or refresh page
- Verify `isOnboardingComplete` field is being read correctly
- Check browser console for JavaScript errors

**Webhook Processing Fails**  
- Verify webhook secrets are configured correctly
- Check webhook URL is accessible from Monite servers
- Monitor webhook event logs for signature verification issues

**User Token Generation Fails**
- Ensure clinic has Monite entity created
- Check user exists in both local DB and Monite
- Verify user has appropriate role permissions

### Debugging Commands
```bash
# Check entity status
php artisan tinker
$clinic = Clinic::find('clinic-id');
$clinic->moniteEntity;

# Test payment methods config
config('monite.payment_methods');

# Monitor logs
tail -f storage/logs/laravel.log | grep -i payment
```

## 🆕 Recent Updates (September 2025)

### Major Architecture Changes

**1. MoniteEntity Model & Database Schema**
- **NEW**: `MoniteEntity` table now stores complete Monite entity data locally
- **REMOVED**: Redundant fields from `clinics` table (`monite_onboarding_status`, `monite_onboarding_requirements`, `monite_onboarding_last_synced_at`)
- **BENEFIT**: Single source of truth, better data integrity, faster queries

**2. Enhanced Entity Management**
```php
// NEW: Automatic data sync after entity creation/update
$entityService->createEntityForClinic($clinic);
// Automatically:
// 1. Creates entity in Monite
// 2. Syncs complete entity details to MoniteEntity table
// 3. Fetches and stores onboarding requirements
// 4. Enables payment methods (card + us_ach)
```

**3. Payment Methods Integration**
- **NEW**: Automatic payment method enablement for both creation AND updates
- **REQUIRED**: `card` payment method (includes Apple Pay, Google Pay)
- **CONFIGURABLE**: Additional methods via `config/monite.php`
- **RESILIENT**: Graceful degradation if payment methods fail

**4. Mailbox Integration**
- **NEW**: Automatic mailbox creation for email-to-invoice processing
- **CONFIGURABLE**: Domain ID and mailbox naming via `config/monite.php`
- **UNIQUE**: Each clinic gets unique mailbox based on clinic name (e.g., `<EMAIL>`)
- **RESILIENT**: Graceful handling of duplicate mailbox errors

**5. Frontend Interface Updates**
- **FIXED**: API response format now uses camelCase for frontend compatibility
- **NEW**: TypeScript interfaces match actual API responses
- **IMPROVED**: React Query hooks with proper caching and refetch logic

```typescript
// NEW: Updated interface matching API response
interface MoniteOnboardingStatusResponse {
  entityId: string;                    // was: entity_id
  onboardingStatus: string;            // was: onboarding_status
  isOnboardingComplete: boolean;       // was: is_onboarding_complete
  // ... other camelCase fields
}
```

**6. Enhanced Webhook Processing**
- **NEW**: Entity webhook events (`entity.updated`, `entity.onboarding_requirements.status_updated`)
- **IMPROVED**: Webhook processing now updates MoniteEntity table directly
- **ADDED**: Complete entity data fetching on webhook events

**7. State Normalization**
- **NEW**: `StateNormalizer` utility for US state validation
- **FIXED**: "Invalid US state" errors in entity creation
- **SUPPORTED**: Full state names → 2-letter abbreviations

### Configuration Updates

**Environment Variables:**
```bash
MONITE_WEBHOOK_SECRET_PAYABLE=your_payable_secret
MONITE_WEBHOOK_SECRET_COUNTERPART=your_counterpart_secret  
MONITE_WEBHOOK_SECRET_ENTITY=your_entity_secret  # NEW
MONITE_MAILBOX_DOMAIN_ID=0f98d790-6fef-42c3-a00e-0608d68f81c5  # NEW
```

**Payment Methods Config:**
```php
'payment_methods' => [
    'receive' => [
        'card',   // REQUIRED by Monite
        'us_ach', // US ACH payments
    ],
    'send' => [
        // Payment methods for vendors (EU/UK only)
    ],
],

    'mailbox' => [
        'domain_id' => env('MONITE_MAILBOX_DOMAIN_ID'),
        'related_object_type' => 'payable',
    ],
```

### API Response Format Changes

**Old Format (snake_case):**
```json
{
  "entity_id": "...",
  "is_onboarding_complete": true
}
```

**New Format (camelCase):**
```json
{
  "entityId": "...",
  "isOnboardingComplete": true
}
```

### Troubleshooting New Issues

**Frontend Shows Wrong Component:**
1. Clear React Query cache (refresh page)
2. Check browser console for JavaScript errors
3. Verify API response format matches TypeScript interface

**Payment Methods Fail:**
1. Ensure `card` is included in config (required)
2. Monitor logs for "Payment methods enabled successfully"
3. Payment failures won't block entity operations

**Mailbox Creation Fails:**
1. Check `MONITE_MAILBOX_DOMAIN_ID` is configured in .env
2. Monitor logs for "Mailbox created successfully" or "Mailbox already exists"
3. Mailbox failures won't block entity operations (graceful degradation)

**Entity Data Not Syncing:**
1. Check `MoniteEntity` table for records
2. Monitor webhook processing logs
3. Verify clinic relationship: `$clinic->moniteEntity`

### Migration Path

For existing implementations:
1. ✅ **Database**: Migration automatically creates `MoniteEntity` table
2. ✅ **API**: Existing endpoints updated with new response format
3. ✅ **Frontend**: TypeScript interfaces updated for camelCase
4. ⚠️ **Manual**: Update any direct references to removed clinic fields

## 📝 License

This module is part of the proprietary HighFive application and is not licensed for public use.