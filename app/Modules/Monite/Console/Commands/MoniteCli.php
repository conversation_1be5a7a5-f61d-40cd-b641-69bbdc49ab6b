<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Services\MoniteEntitySettingsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Throwable;

class MoniteCli extends Command
{
    protected $signature = 'monite:cli 
                            {action : Action to perform (status|list-webhooks|register-webhook|delete-webhook|update-settings|get-settings|list-domains|create-domain|verify-domain|delete-domain|list-mailboxes|create-mailbox|delete-mailbox)}
                            {--clinic-id= : Process for specific clinic}
                            {--webhook-url= : Webhook URL for registration}
                            {--object-type= : Object type for webhook (payable|counterpart|entity)}
                            {--webhook-id= : Webhook ID for deletion}
                            {--setting= : Setting name to update (e.g., allow_cancel_duplicates_automatically)}
                            {--value= : Setting value (true|false)}
                            {--domain= : Domain name for domain operations}
                            {--domain-id= : Domain ID for domain operations}
                            {--mailbox-name= : Mailbox name for mailbox operations}
                            {--mailbox-id= : Mailbox ID for mailbox operations}
                            {--related-object-type= : Related object type for mailbox (payable|receipt)}
                            {--dry-run : Preview changes without applying them}';

    protected $description = 'Monite CLI - Manage webhooks, settings, domains, mailboxes, and other Monite operations';

    public function __construct(
        private readonly MoniteApiClientInterface $moniteClient,
        private readonly MoniteEntitySettingsService $settingsService
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $action = $this->argument('action');
        $clinicId = $this->option('clinic-id');

        try {
            return match ($action) {
                'status' => $this->showStatus($clinicId),
                'list-webhooks' => $this->listWebhooks($clinicId),
                'register-webhook' => $this->registerWebhook($clinicId),
                'delete-webhook' => $this->deleteWebhook($clinicId),
                'update-settings' => $this->updateSettings($clinicId),
                'get-settings' => $this->getSettings($clinicId),
                'list-domains' => $this->listDomains(),
                'create-domain' => $this->createDomain(),
                'verify-domain' => $this->verifyDomain(),
                'delete-domain' => $this->deleteDomain(),
                'list-mailboxes' => $this->listMailboxes($clinicId),
                'create-mailbox' => $this->createMailbox($clinicId),
                'delete-mailbox' => $this->deleteMailbox($clinicId),
                default => $this->showHelp(),
            };
        } catch (Throwable $e) {
            $this->error("Error: {$e->getMessage()}");
            Log::error('Monite webhook command failed', [
                'action' => $action,
                'clinic_id' => $clinicId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * Show current webhook and settings status
     */
    private function showStatus(?string $clinicId): int
    {
        if ($clinicId) {
            return $this->showClinicStatus($clinicId);
        }

        $this->info('Monite Webhook and Settings Status');
        $this->line('');

        // Show partner-level webhook configuration
        $this->showPartnerWebhookConfig();

        // Show entity-level settings for all clinics
        $this->showEntitySettings();

        return 0;
    }

    /**
     * Show status for a specific clinic
     */
    private function showClinicStatus(string $clinicId): int
    {
        $clinic = $this->findClinic($clinicId);
        if (! $clinic) {
            return 1;
        }

        if (! $clinic->monite_entity_id) {
            $this->error("Clinic {$clinic->name} has no Monite entity ID.");

            return 1;
        }

        $this->info("Monite Settings for Clinic: {$clinic->name}");
        $this->line("Entity ID: {$clinic->monite_entity_id}");
        $this->line('');

        try {
            $settings = $this->settingsService->getSettings($clinic);
            $this->displaySettings($settings);
        } catch (MoniteApiException $e) {
            $this->error("Failed to retrieve settings: {$e->getMessage()}");

            return 1;
        }

        return 0;
    }

    /**
     * Show partner-level webhook configuration
     */
    private function showPartnerWebhookConfig(): void
    {
        $this->info('📡 Partner Webhook Configuration:');

        $webhookUrl = config('monite.webhook_url');
        $this->line("Webhook URL: {$webhookUrl}");

        $secrets = config('monite.webhook_secrets', []);
        if (empty($secrets)) {
            $this->warn('⚠️  No webhook secrets configured');
        } else {
            $this->line('Webhook Secrets:');
            foreach ($secrets as $type => $secret) {
                $masked = $secret ? mb_substr($secret, 0, 8).'...' : 'Not set';
                $this->line("  {$type}: {$masked}");
            }
        }

        $this->line('');
    }

    /**
     * Show entity settings for all clinics
     */
    private function showEntitySettings(): void
    {
        $this->info('🏥 Entity Settings:');

        $clinics = Clinic::whereNotNull('monite_entity_id')->get();

        if ($clinics->isEmpty()) {
            $this->warn('No clinics with Monite entities found.');

            return;
        }

        $headers = ['Clinic', 'Entity ID', 'Auto-Cancel Duplicates'];
        $rows = [];

        foreach ($clinics as $clinic) {
            try {
                $settings = $this->settingsService->getSettings($clinic);
                $payableSettings = $settings['payable'] ?? [];

                $rows[] = [
                    $clinic->name,
                    mb_substr($clinic->monite_entity_id, 0, 8).'...',
                    $this->getSettingStatus($payableSettings, 'allow_cancel_duplicates_automatically'),
                ];
            } catch (MoniteApiException $e) {
                $rows[] = [
                    $clinic->name,
                    mb_substr($clinic->monite_entity_id, 0, 8).'...',
                    '❌ Error',
                ];
            }
        }

        $this->table($headers, $rows);
    }

    /**
     * Update settings for a clinic or all clinics
     */
    private function updateSettings(?string $clinicId): int
    {
        $setting = $this->option('setting');
        $value = $this->option('value');

        if (! $setting) {
            $this->error('Setting name is required. Use --setting=setting_name');

            return 1;
        }

        if (! $value) {
            $this->error('Setting value is required. Use --value=true or --value=false');

            return 1;
        }

        $boolValue = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
        if ($boolValue === null) {
            $this->error('Setting value must be true or false');

            return 1;
        }

        $action = $boolValue ? 'enable' : 'disable';

        if ($clinicId) {
            return $this->updateClinicSetting($clinicId, $setting, $boolValue, $action);
        }

        return $this->updateAllClinicsSetting($setting, $boolValue, $action);
    }

    /**
     * Get settings for a clinic or all clinics
     */
    private function getSettings(?string $clinicId): int
    {
        if ($clinicId) {
            return $this->getClinicSettings($clinicId);
        }

        return $this->getAllClinicsSettings();
    }

    /**
     * Get settings for a specific clinic
     */
    private function getClinicSettings(string $clinicId): int
    {
        $clinic = $this->findClinic($clinicId);
        if (! $clinic) {
            return 1;
        }

        try {
            $settings = $this->settingsService->getSettings($clinic);
            $this->info("Settings for {$clinic->name}:");
            $this->displaySettings($settings);

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to get settings for {$clinic->name}: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Get settings for all clinics
     */
    private function getAllClinicsSettings(): int
    {
        $clinics = Clinic::whereNotNull('monite_entity_id')->get();

        if ($clinics->isEmpty()) {
            $this->warn('No clinics with Monite entities found.');

            return 0;
        }

        foreach ($clinics as $clinic) {
            try {
                $settings = $this->settingsService->getSettings($clinic);
                $this->info("Settings for {$clinic->name}:");
                $this->displaySettings($settings);
                $this->line('');
            } catch (MoniteApiException $e) {
                $this->error("Failed to get settings for {$clinic->name}: {$e->getMessage()}");
            }
        }

        return 0;
    }

    /**
     * Display settings in a readable format
     */
    private function displaySettings(array $settings): void
    {
        $payableSettings = $settings['payable'] ?? [];

        if (empty($payableSettings)) {
            $this->warn('  No payable settings found');

            return;
        }

        foreach ($payableSettings as $key => $value) {
            $status = $this->getSettingStatus($payableSettings, $key);
            $this->line("  {$key}: {$status}");
        }
    }

    /**
     * Update setting for a specific clinic
     */
    private function updateClinicSetting(string $clinicId, string $setting, bool $value, string $action): int
    {
        $clinic = $this->findClinic($clinicId);
        if (! $clinic) {
            return 1;
        }

        if (! $clinic->monite_entity_id) {
            $this->error("Clinic {$clinic->name} has no Monite entity ID.");

            return 1;
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would {$action} {$setting} for clinic {$clinic->name}");

            return 0;
        }

        try {
            $settingsData = [
                'payable' => [
                    $setting => $value,
                    'approve_page_url' => config('highfive.monite.approve_page_url', config('app.url')),
                ],
            ];

            $this->settingsService->updateSettings($clinic, $settingsData, "{$action} {$setting}");
            $this->info("✅ Successfully {$action}d {$setting} for clinic {$clinic->name}");

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to {$action} {$setting} for clinic {$clinic->name}: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Update setting for all clinics
     */
    private function updateAllClinicsSetting(string $setting, bool $value, string $action): int
    {
        $clinics = Clinic::whereNotNull('monite_entity_id')->get();

        if ($clinics->isEmpty()) {
            $this->warn('No clinics with Monite entities found.');

            return 0;
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would {$action} {$setting} for {$clinics->count()} clinic(s)");

            return 0;
        }

        $this->info("{$action}ing {$setting} for {$clinics->count()} clinic(s)...");

        $successCount = 0;
        $errorCount = 0;

        foreach ($clinics as $clinic) {
            try {
                $settingsData = [
                    'payable' => [
                        $setting => $value,
                        'approve_page_url' => config('highfive.monite.approve_page_url', config('app.url')),
                    ],
                ];

                $this->settingsService->updateSettings($clinic, $settingsData, "{$action} {$setting}");
                $this->line("✅ {$clinic->name}");
                $successCount++;
            } catch (MoniteApiException $e) {
                $this->error("❌ {$clinic->name}: {$e->getMessage()}");
                $errorCount++;
            }
        }

        $this->info("Completed: {$successCount} successful, {$errorCount} failed");

        return $errorCount > 0 ? 1 : 0;
    }

    /**
     * List webhooks for a clinic
     */
    private function listWebhooks(?string $clinicId): int
    {
        if ($clinicId) {
            $clinic = $this->findClinic($clinicId);
            if (! $clinic) {
                return 1;
            }

            if (! $clinic->monite_entity_id) {
                $this->error("Clinic {$clinic->name} has no Monite entity ID.");

                return 1;
            }

            $this->info("Webhooks for Clinic: {$clinic->name}");
            $this->line("Entity ID: {$clinic->monite_entity_id}");
        } else {
            $this->info('Partner-level Webhooks');
        }

        try {
            $client = $clinicId ? $this->moniteClient->withEntityId($clinic->monite_entity_id) : $this->moniteClient;
            $response = $client->get('/webhook_subscriptions');

            if (! $response->successful()) {
                $this->error("Failed to retrieve webhooks: {$response->body()}");

                return 1;
            }

            $webhooks = $response->json()['data'] ?? [];

            if (empty($webhooks)) {
                $this->warn('No webhooks found.');

                return 0;
            }

            $headers = ['ID', 'URL', 'Object Type', 'Event Types', 'Status'];
            $rows = [];

            foreach ($webhooks as $webhook) {
                $eventTypes = isset($webhook['event_types'])
                    ? implode(', ', $webhook['event_types'])
                    : 'N/A';

                $rows[] = [
                    mb_substr($webhook['id'], 0, 8).'...',
                    $webhook['url'],
                    $webhook['object_type'] ?? 'N/A',
                    $eventTypes,
                    $webhook['status'] ?? 'N/A',
                ];
            }

            $this->table($headers, $rows);

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to list webhooks: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Register a new webhook
     */
    private function registerWebhook(?string $clinicId): int
    {
        $webhookUrl = $this->option('webhook-url');
        $objectType = $this->option('object-type');

        if (! $webhookUrl) {
            $this->error('Webhook URL is required. Use --webhook-url option.');

            return 1;
        }

        if (! $objectType) {
            $this->error('Object type is required. Use --object-type option.');

            return 1;
        }

        if ($clinicId) {
            $clinic = $this->findClinic($clinicId);
            if (! $clinic) {
                return 1;
            }

            if (! $clinic->monite_entity_id) {
                $this->error("Clinic {$clinic->name} has no Monite entity ID.");

                return 1;
            }

            $entityId = $clinic->monite_entity_id;
            $this->info("Registering webhook for clinic: {$clinic->name}");
        } else {
            $entityId = null;
            $this->info('Registering partner-level webhook');
        }

        if ($this->option('dry-run')) {
            $this->info('DRY RUN: Would register webhook:');
            $this->line("  URL: {$webhookUrl}");
            $this->line("  Object Type: {$objectType}");
            $this->line('  Entity ID: '.($entityId ? mb_substr($entityId, 0, 8).'...' : 'Partner-level'));

            return 0;
        }

        try {
            $webhookData = [
                'url' => $webhookUrl,
                'object_type' => $objectType,
                'event_types' => $this->getDefaultEventTypes($objectType),
            ];

            $client = $entityId ? $this->moniteClient->withEntityId($entityId) : $this->moniteClient;
            $response = $client->post('/webhook_subscriptions', $webhookData);

            if (! $response->successful()) {
                $this->error("Failed to register webhook: {$response->body()}");

                return 1;
            }

            $webhook = $response->json();
            $this->info('✅ Webhook registered successfully');
            $this->line("ID: {$webhook['id']}");
            $this->line("URL: {$webhook['url']}");
            $this->line("Object Type: {$webhook['object_type']}");

            if (isset($webhook['secret'])) {
                $this->line('');
                $this->warn('🔐 IMPORTANT: Save this webhook secret to your .env file:');
                $this->line('MONITE_WEBHOOK_SECRET_'.mb_strtoupper($objectType)."={$webhook['secret']}");
            }

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to register webhook: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Delete a webhook
     */
    private function deleteWebhook(?string $clinicId): int
    {
        $webhookId = $this->option('webhook-id');

        if (! $webhookId) {
            $this->error('Webhook ID is required. Use --webhook-id option.');

            return 1;
        }

        if ($clinicId) {
            $clinic = $this->findClinic($clinicId);
            if (! $clinic) {
                return 1;
            }

            if (! $clinic->monite_entity_id) {
                $this->error("Clinic {$clinic->name} has no Monite entity ID.");

                return 1;
            }

            $entityId = $clinic->monite_entity_id;
            $this->info("Deleting webhook for clinic: {$clinic->name}");
        } else {
            $entityId = null;
            $this->info('Deleting partner-level webhook');
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would delete webhook: {$webhookId}");

            return 0;
        }

        try {
            $client = $entityId ? $this->moniteClient->withEntityId($entityId) : $this->moniteClient;
            $response = $client->delete("/webhook_subscriptions/{$webhookId}");

            if (! $response->successful()) {
                $this->error("Failed to delete webhook: {$response->body()}");

                return 1;
            }

            $this->info('✅ Webhook deleted successfully');

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to delete webhook: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Show help information
     */
    private function showHelp(): int
    {
        $this->info('Monite Webhook Management Command');
        $this->line('');
        $this->line('Available actions:');
        $this->line('  status              - Show current webhook and settings status');
        $this->line('  enable-autolink     - Enable counterpart auto-linking');
        $this->line('  disable-autolink    - Disable counterpart auto-linking');
        $this->line('  enable-autocreate   - Enable counterpart auto-creation');
        $this->line('  disable-autocreate  - Disable counterpart auto-creation');
        $this->line('  list-webhooks       - List all webhooks');
        $this->line('  register-webhook    - Register a new webhook');
        $this->line('  delete-webhook      - Delete a webhook');
        $this->line('');
        $this->line('Options:');
        $this->line('  --clinic-id=ID      - Process for specific clinic');
        $this->line('  --webhook-url=URL   - Webhook URL for registration');
        $this->line('  --object-type=TYPE  - Object type for webhook (payable|counterpart|entity)');
        $this->line('  --webhook-id=ID     - Webhook ID for deletion');
        $this->line('  --dry-run           - Preview changes without applying them');
        $this->line('');
        $this->line('Examples:');
        $this->line('  php artisan monite:webhook status');
        $this->line('  php artisan monite:webhook enable-autolink --clinic-id=123');
        $this->line('  php artisan monite:webhook list-webhooks --clinic-id=123');
        $this->line('  php artisan monite:webhook register-webhook --webhook-url=https://example.com/webhook --object-type=payable');
        $this->line('  php artisan monite:webhook delete-webhook --webhook-id=abc123');

        return 0;
    }

    /**
     * Find a clinic by ID
     */
    private function findClinic(string $clinicId): ?Clinic
    {
        try {
            $clinic = Clinic::find($clinicId);

            if (! $clinic) {
                $this->error("Clinic with ID {$clinicId} not found.");

                return null;
            }

            return $clinic;
        } catch (Throwable $e) {
            $this->error("Invalid clinic ID format: {$clinicId}");

            return null;
        }
    }

    /**
     * Get a human-readable status for a setting
     */
    private function getSettingStatus(array $settings, string $key): string
    {
        $value = $settings[$key] ?? null;

        return match ($value) {
            true => '✅ Enabled',
            false => '❌ Disabled',
            default => '❓ Not set',
        };
    }

    /**
     * Get default event types for an object type
     * These match the SUPPORTED_EVENTS in MoniteWebhookController
     */
    private function getDefaultEventTypes(string $objectType): array
    {
        return match ($objectType) {
            'payable' => ['created', 'updated', 'ocr_finished'],
            'counterpart' => ['created'],
            'entity' => ['onboarding_requirements.status_updated', 'updated', 'onboarding_requirements.updated'],
            default => ['created', 'updated'],
        };
    }

    // ==================== DOMAIN MANAGEMENT ====================

    /**
     * List all domains
     */
    private function listDomains(): int
    {
        $this->info('📧 Partner Domains:');

        try {
            $response = $this->moniteClient->get('/mailbox_domains');

            if (! $response->successful()) {
                $this->error("Failed to retrieve domains: {$response->body()}");

                return 1;
            }

            $data = $response->json();
            $domains = $data['data'] ?? [];

            if (empty($domains)) {
                $this->warn('No domains found.');

                return 0;
            }

            $headers = ['ID', 'Domain', 'Status', 'Dedicated IP', 'Last Updated'];
            $rows = [];

            foreach ($domains as $domain) {
                $rows[] = [
                    mb_substr($domain['id'], 0, 8).'...',
                    $domain['domain'],
                    $this->getDomainStatus($domain['status']),
                    $domain['dedicated_ip'] ?? 'N/A',
                    $domain['last_updated_at'] ?? 'N/A',
                ];
            }

            $this->table($headers, $rows);

            // Show DNS records for each domain
            foreach ($domains as $domain) {
                $this->showDomainDnsRecords($domain);
            }

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to list domains: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Create a new domain
     */
    private function createDomain(): int
    {
        $domain = $this->option('domain');

        if (! $domain) {
            $this->error('Domain name is required. Use --domain=example.com');

            return 1;
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would create domain: {$domain}");

            return 0;
        }

        try {
            $domainData = [
                'domain' => $domain,
            ];

            $response = $this->moniteClient->post('/mailbox_domains', $domainData);

            if (! $response->successful()) {
                $this->error("Failed to create domain: {$response->body()}");

                return 1;
            }

            $domainInfo = $response->json();
            $this->info('✅ Domain created successfully');
            $this->line("ID: {$domainInfo['id']}");
            $this->line("Domain: {$domainInfo['domain']}");
            $this->line("Status: {$this->getDomainStatus($domainInfo['status'])}");

            // Show DNS records
            $this->showDomainDnsRecords($domainInfo);

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to create domain: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Verify a domain
     */
    private function verifyDomain(): int
    {
        $domainId = $this->option('domain-id');

        if (! $domainId) {
            $this->error('Domain ID is required. Use --domain-id=domain_id');

            return 1;
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would verify domain: {$domainId}");

            return 0;
        }

        try {
            $response = $this->moniteClient->post("/mailbox_domains/{$domainId}/verify");

            if (! $response->successful()) {
                $this->error("Failed to verify domain: {$response->body()}");

                return 1;
            }

            $domainInfo = $response->json();
            $this->info('✅ Domain verification completed');
            $this->line("Domain: {$domainInfo['domain']}");
            $this->line("Status: {$this->getDomainStatus($domainInfo['status'])}");

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to verify domain: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Delete a domain
     */
    private function deleteDomain(): int
    {
        $domainId = $this->option('domain-id');

        if (! $domainId) {
            $this->error('Domain ID is required. Use --domain-id=domain_id');

            return 1;
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would delete domain: {$domainId}");

            return 0;
        }

        try {
            $response = $this->moniteClient->delete("/mailbox_domains/{$domainId}");

            if (! $response->successful()) {
                $this->error("Failed to delete domain: {$response->body()}");

                return 1;
            }

            $this->info('✅ Domain deleted successfully');

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to delete domain: {$e->getMessage()}");

            return 1;
        }
    }

    // ==================== MAILBOX MANAGEMENT ====================

    /**
     * List all mailboxes for a clinic or all clinics
     */
    private function listMailboxes(?string $clinicId): int
    {
        if ($clinicId) {
            return $this->listClinicMailboxes($clinicId);
        }

        return $this->listAllClinicsMailboxes();
    }

    /**
     * List mailboxes for a specific clinic
     */
    private function listClinicMailboxes(string $clinicId): int
    {
        $clinic = $this->findClinic($clinicId);
        if (! $clinic) {
            return 1;
        }

        $this->info("📧 Mailboxes for {$clinic->name}:");

        try {
            $client = $this->moniteClient->withEntityId($clinic->monite_entity_id);
            $response = $client->get('/mailboxes');

            if (! $response->successful()) {
                $this->error("Failed to retrieve mailboxes: {$response->body()}");

                return 1;
            }

            $data = $response->json();
            $mailboxes = $data['data'] ?? [];

            if (empty($mailboxes)) {
                $this->warn('No mailboxes found for this clinic.');

                return 0;
            }

            $this->displayMailboxes($mailboxes);

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to list mailboxes: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * List mailboxes for all clinics
     */
    private function listAllClinicsMailboxes(): int
    {
        $clinics = Clinic::whereNotNull('monite_entity_id')->get();

        if ($clinics->isEmpty()) {
            $this->warn('No clinics with Monite entities found.');

            return 0;
        }

        foreach ($clinics as $clinic) {
            $this->listClinicMailboxes($clinic->id);
            $this->line('');
        }

        return 0;
    }

    /**
     * Create a mailbox for a clinic
     */
    private function createMailbox(?string $clinicId): int
    {
        if (! $clinicId) {
            $this->error('Clinic ID is required for mailbox creation. Use --clinic-id=clinic_id');

            return 1;
        }

        $clinic = $this->findClinic($clinicId);
        if (! $clinic) {
            return 1;
        }

        $mailboxName = $this->option('mailbox-name');
        $relatedObjectType = $this->option('related-object-type', 'payable');
        $domainId = $this->option('domain-id');

        if (! $mailboxName) {
            $this->error('Mailbox name is required. Use --mailbox-name=mailbox_name');

            return 1;
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would create mailbox for {$clinic->name}:");
            $this->line("  Mailbox Name: {$mailboxName}");
            $this->line("  Related Object Type: {$relatedObjectType}");
            $this->line('  Domain ID: '.($domainId ?: 'Default Monite domain'));

            return 0;
        }

        try {
            $mailboxData = [
                'related_object_type' => $relatedObjectType,
                'mailbox_name' => $mailboxName,
            ];

            // Always include domain_id - use null for default Monite domain if not provided
            $mailboxData['mailbox_domain_id'] = $domainId;

            $client = $this->moniteClient->withEntityId($clinic->monite_entity_id);
            $response = $client->post('/mailboxes', $mailboxData);

            if (! $response->successful()) {
                $this->error("Failed to create mailbox: {$response->body()}");

                return 1;
            }

            $mailboxInfo = $response->json();
            $this->info("✅ Mailbox created successfully for {$clinic->name}");
            $this->line("ID: {$mailboxInfo['id']}");
            $this->line("Full Address: {$mailboxInfo['mailbox_full_address']}");
            $this->line("Status: {$this->getMailboxStatus($mailboxInfo['status'])}");

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to create mailbox: {$e->getMessage()}");

            return 1;
        }
    }

    /**
     * Delete a mailbox
     */
    private function deleteMailbox(?string $clinicId): int
    {
        if (! $clinicId) {
            $this->error('Clinic ID is required for mailbox deletion. Use --clinic-id=clinic_id');

            return 1;
        }

        $clinic = $this->findClinic($clinicId);
        if (! $clinic) {
            return 1;
        }

        $mailboxId = $this->option('mailbox-id');

        if (! $mailboxId) {
            $this->error('Mailbox ID is required. Use --mailbox-id=mailbox_id');

            return 1;
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would delete mailbox {$mailboxId} for {$clinic->name}");

            return 0;
        }

        try {
            $client = $this->moniteClient->withEntityId($clinic->monite_entity_id);
            $response = $client->delete("/mailboxes/{$mailboxId}");

            if (! $response->successful()) {
                $this->error("Failed to delete mailbox: {$response->body()}");

                return 1;
            }

            $this->info("✅ Mailbox deleted successfully for {$clinic->name}");

            return 0;
        } catch (MoniteApiException $e) {
            $this->error("Failed to delete mailbox: {$e->getMessage()}");

            return 1;
        }
    }

    // ==================== HELPER METHODS ====================

    /**
     * Get domain status with emoji
     */
    private function getDomainStatus(string $status): string
    {
        return match ($status) {
            'verified' => '✅ Verified',
            'waiting_to_be_verified' => '⏳ Waiting for verification',
            default => "❓ {$status}",
        };
    }

    /**
     * Get mailbox status with emoji
     */
    private function getMailboxStatus(string $status): string
    {
        return match ($status) {
            'active' => '✅ Active',
            'inactive' => '❌ Inactive',
            default => "❓ {$status}",
        };
    }

    /**
     * Show DNS records for a domain
     */
    private function showDomainDnsRecords(array $domain): void
    {
        $dnsRecords = $domain['dns_records'] ?? [];

        if (empty($dnsRecords)) {
            return;
        }

        $this->line("DNS Records for {$domain['domain']}:");

        // Sending DNS records
        if (! empty($dnsRecords['sending_dns_records'])) {
            $this->line('  📤 Sending Records:');
            foreach ($dnsRecords['sending_dns_records'] as $record) {
                $status = $record['valid'] === 'valid' ? '✅' : '❌';
                $this->line("    {$status} {$record['record_type']} {$record['name']}: {$record['value']}");
            }
        }

        // Receiving DNS records
        if (! empty($dnsRecords['receiving_dns_records'])) {
            $this->line('  📥 Receiving Records:');
            foreach ($dnsRecords['receiving_dns_records'] as $record) {
                $status = $record['valid'] === 'valid' ? '✅' : '❌';
                $this->line("    {$status} {$record['record_type']} {$record['name']}: {$record['value']}");
            }
        }

        $this->line('');
    }

    /**
     * Display mailboxes in a table format
     */
    private function displayMailboxes(array $mailboxes): void
    {
        $headers = ['ID', 'Full Address', 'Object Type', 'Status'];
        $rows = [];

        foreach ($mailboxes as $mailbox) {
            $rows[] = [
                mb_substr($mailbox['id'], 0, 8).'...',
                $mailbox['mailbox_full_address'],
                $mailbox['related_object_type'],
                $this->getMailboxStatus($mailbox['status']),
            ];
        }

        $this->table($headers, $rows);
    }
}
