<?php

declare(strict_types=1);

namespace App\Modules\Monite\Models;

use App\Models\Clinic;
use DateTimeImmutable;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MoniteEntity extends Model
{
    use HasFactory;
    use HasUuids;
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'entity_data' => 'array',
        'onboarding_requirements' => 'array',
        'monite_created_at' => 'datetime',
        'monite_updated_at' => 'datetime',
    ];

    /**
     * Get the clinic that owns the Monite entity.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Update entity from Monite API response data
     */
    public function updateFromMoniteData(array $entityData, ?array $onboardingRequirements = null): void
    {
        $this->update([
            'entity_data' => $entityData,
            'onboarding_requirements' => $onboardingRequirements,
            'status' => $entityData['status'] ?? null,
            'type' => $entityData['type'] ?? null,
            'email' => $entityData['email'] ?? null,
            'monite_created_at' => isset($entityData['created_at']) ? new DateTimeImmutable($entityData['created_at']) : null,
            'monite_updated_at' => isset($entityData['updated_at']) ? new DateTimeImmutable($entityData['updated_at']) : null,
        ]);
    }

    /**
     * Check if onboarding is complete
     */
    public function isOnboardingComplete(): bool
    {
        // Check verification_status in onboarding_requirements
        // 'enabled' = onboarding complete, 'disabled' = onboarding incomplete
        return ($this->onboarding_requirements['verification_status'] ?? null) === 'enabled';
    }
}
