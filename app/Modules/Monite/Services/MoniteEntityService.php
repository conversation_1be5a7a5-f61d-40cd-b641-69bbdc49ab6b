<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Models\MoniteEntity;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use App\Support\PhoneNumberFormatter;
use App\Support\StateNormalizer;
use DateTimeImmutable;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MoniteEntityService
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteApiClientInterface $moniteClient
    ) {}

    /**
     * Create a Monite entity for a clinic and store the reference
     */
    public function createEntityForClinic(Clinic $clinic): ?string
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'createEntityForClinic')) {
            return null;
        }

        if ($clinic->monite_entity_id) {
            throw new MoniteApiException("Clinic already has a Monite entity: {$clinic->monite_entity_id}");
        }

        try {
            $entityData = $this->prepareEntityData($clinic);

            Log::info('Creating Monite entity - Request payload', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'payload' => $entityData,
            ]);

            $response = $this->moniteClient->post('/entities', $entityData);

            if (! $response->successful()) {
                Log::error('Monite entity creation failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            $responseData = $response->json();
            $entityId = $responseData['id'];

            // Store the entity ID in the clinic
            $clinic->update(['monite_entity_id' => $entityId]);

            Log::info('Monite entity created for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $entityId,
            ]);

            // Fetch complete entity details and onboarding requirements
            $this->syncEntityDetailsToLocalDb($clinic, $entityId);

            // Enable payment methods for the entity
            $this->enablePaymentMethods($clinic, $entityId);

            // Create mailbox for the entity as final step
            $this->createMailboxForEntity($entityId, $clinic);

            return $entityId;

        } catch (MoniteApiException $e) {
            Log::error('Failed to create Monite entity for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Update a Monite entity when clinic details change
     */
    public function updateEntityForClinic(Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'updateEntityForClinic')) {
            return;
        }

        if (! $clinic->monite_entity_id) {
            Log::warning('Attempted to update Monite entity for clinic without entity ID', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
            ]);

            return;
        }

        try {
            $entityData = $this->prepareUpdateEntityData($clinic);

            Log::info('Updating Monite entity - Request payload', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
                'payload' => $entityData,
            ]);

            $response = $this->moniteClient
                ->withEntityId($clinic->monite_entity_id)
                ->patch("/entities/{$clinic->monite_entity_id}", $entityData);

            if (! $response->successful()) {
                Log::error('Monite entity update failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'monite_entity_id' => $clinic->monite_entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            // Ensure mailbox exists for the entity
            $this->createMailboxForEntity($clinic->monite_entity_id, $clinic);

            Log::info('Monite entity updated for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
            ]);

            // Sync updated entity details to local database
            $this->syncEntityDetailsToLocalDb($clinic, $clinic->monite_entity_id);

            // Enable/update payment methods for the entity
            $this->enablePaymentMethods($clinic, $clinic->monite_entity_id);

        } catch (MoniteApiException $e) {
            Log::error('Failed to update Monite entity for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get or create Monite entity for a clinic
     */
    public function getOrCreateEntityForClinic(Clinic $clinic): ?string
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'getOrCreateEntityForClinic')) {
            return null;
        }

        if ($clinic->monite_entity_id) {
            return $clinic->monite_entity_id;
        }

        return $this->createEntityForClinic($clinic);
    }

    /**
     * Delete a Monite entity for a clinic
     */
    public function deleteEntityForClinic(Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'deleteEntityForClinic')) {
            return;
        }

        if (! $clinic->monite_entity_id) {
            return;
        }

        try {
            $response = $this->moniteClient->delete("/entities/{$clinic->monite_entity_id}");

            if (! $response->successful() && $response->status() !== 404) {
                throw MoniteApiException::fromResponse($response);
            }

            // Clear the entity ID from the clinic
            $clinic->update(['monite_entity_id' => null]);

            Log::info('Monite entity deleted for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
            ]);

        } catch (MoniteApiException $e) {
            Log::error('Failed to delete Monite entity for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Prepare entity data for Monite API entity updates based on clinic information
     */
    private function prepareUpdateEntityData(Clinic $clinic): array
    {
        $entityData = [
            'email' => $this->getClinicEmail($clinic),
            'organization' => [
                'legal_name' => $clinic->name,
            ],
        ];

        // Add tax ID if available
        if ($clinic->business_tax_id) {
            $entityData['tax_id'] = $clinic->business_tax_id;
        }

        // Add phone number if available
        if ($clinic->phone_number) {
            $formattedPhone = PhoneNumberFormatter::formatToE164($clinic->phone_number);
            if ($formattedPhone) {
                $entityData['phone'] = $formattedPhone;
            } else {
                Log::warning('Invalid phone number format for clinic', [
                    'clinic_id' => $clinic->id,
                    'phone_number' => $clinic->phone_number,
                ]);
            }
        }

        // Add address - required field for Monite API
        if ($clinic->billingAddress) {
            $address = $clinic->billingAddress;
            $entityData['address'] = [
                'city' => $address->city,
                'state' => StateNormalizer::normalize($address->state),
                'postal_code' => $address->postal_code,
                'line1' => $address->street,
            ];

            if ($address->state) {
                $normalizedState = StateNormalizer::normalize($address->state);
                if ($normalizedState) {
                    $entityData['address']['state'] = $normalizedState;
                } else {
                    Log::warning('Invalid state format for clinic', [
                        'clinic_id' => $clinic->id,
                        'state' => $address->state,
                    ]);
                }
            }
        } else {
            // Fallback address when clinic doesn't have billing address
            $entityData['address'] = [
                'city' => 'New York',
                'state' => 'NY',
                'postal_code' => '10001',
                'line1' => 'TBD',
            ];
        }

        return $entityData;
    }

    /**
     * Prepare entity data for Monite API entity creation based on clinic information
     */
    private function prepareEntityData(Clinic $clinic): array
    {
        $entityData = [
            'type' => 'organization',
            'email' => $this->getClinicEmail($clinic),
            'organization' => [
                'legal_name' => $clinic->name,
            ],
        ];

        // Add tax ID if available
        if ($clinic->business_tax_id) {
            $entityData['tax_id'] = $clinic->business_tax_id;
        }

        // Add phone number if available
        if ($clinic->phone_number) {
            $formattedPhone = PhoneNumberFormatter::formatToE164($clinic->phone_number);
            if ($formattedPhone) {
                $entityData['phone'] = $formattedPhone;
            } else {
                Log::warning('Invalid phone number format for clinic', [
                    'clinic_id' => $clinic->id,
                    'phone_number' => $clinic->phone_number,
                ]);
            }
        }

        // Add address - required field for Monite API
        if ($clinic->billingAddress) {
            $address = $clinic->billingAddress;
            $entityData['address'] = [
                'country' => $address->country ?? 'US',
                'city' => $address->city,
                'state' => StateNormalizer::normalize($address->state),
                'postal_code' => $address->postal_code,
                'line1' => $address->street,
            ];

            if ($address->state) {
                $normalizedState = StateNormalizer::normalize($address->state);
                if ($normalizedState) {
                    $entityData['address']['state'] = $normalizedState;
                } else {
                    Log::warning('Invalid state format for clinic', [
                        'clinic_id' => $clinic->id,
                        'state' => $address->state,
                    ]);
                }
            }
        } else {
            // Fallback address when clinic doesn't have billing address
            $entityData['address'] = [
                'country' => 'US',
                'city' => 'New York',
                'postal_code' => '10001',
                'state' => 'NY', // Required for US addresses
                'line1' => 'TBD', // To Be Determined - placeholder
            ];
        }

        return $entityData;
    }

    /**
     * Get email for clinic - we'll need to determine the best way to get this
     * For now, we'll use a placeholder approach
     */
    private function getClinicEmail(Clinic $clinic): string
    {
        // Try to get email from clinic managers first
        if ($clinic->managers?->first()?->email) {
            return $clinic->managers->first()->email;
        }

        // Try to get email from any clinic user
        if ($clinic->users?->first()?->email) {
            return $clinic->users->first()->email;
        }

        // Fallback to clinic ID-based email
        return "{$clinic->id}@highfive.vet";
    }

    /**
     * Sync entity details and onboarding requirements to local database
     */
    private function syncEntityDetailsToLocalDb(Clinic $clinic, string $entityId): void
    {
        try {
            // Fetch complete entity details
            $entityDetails = $this->fetchEntityDetails($entityId);

            // Fetch onboarding requirements
            $onboardingRequirements = $this->fetchOnboardingRequirements($entityId);

            // Create or update MoniteEntity record
            $this->createOrUpdateMoniteEntity($clinic, $entityDetails, $onboardingRequirements);

            Log::info('Entity details synced to local database', [
                'clinic_id' => $clinic->id,
                'entity_id' => $entityId,
                'status' => $entityDetails['status'] ?? null,
            ]);
        } catch (Exception $e) {
            // Log but don't fail the entity creation if sync fails
            Log::warning('Failed to sync entity details to local database', [
                'clinic_id' => $clinic->id,
                'entity_id' => $entityId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Fetch complete entity details from Monite API
     */
    private function fetchEntityDetails(string $entityId): array
    {
        $response = $this->moniteClient
            ->withEntityId($entityId)
            ->get('/entities/me');

        if (! $response->successful()) {
            throw new MoniteApiException('Failed to fetch entity details: '.$response->body());
        }

        return $response->json();
    }

    /**
     * Fetch onboarding requirements from Monite API
     */
    private function fetchOnboardingRequirements(string $entityId): ?array
    {
        try {
            $response = $this->moniteClient
                ->withEntityId($entityId)
                ->get('/onboarding_requirements');

            if ($response->successful()) {
                $responseData = $response->json();

                // Return the first object from data array, not the array
                return $responseData['data'][0] ?? null;
            }

            Log::warning('Failed to fetch onboarding requirements', [
                'entity_id' => $entityId,
                'status' => $response->status(),
            ]);

            return null;
        } catch (Exception $e) {
            Log::warning('Exception fetching onboarding requirements', [
                'entity_id' => $entityId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Create or update MoniteEntity record with full entity data
     */
    private function createOrUpdateMoniteEntity(Clinic $clinic, array $entityDetails, ?array $onboardingRequirements = null): void
    {
        $moniteEntity = MoniteEntity::updateOrCreate(
            [
                'monite_entity_id' => $clinic->monite_entity_id,
            ],
            [
                'clinic_id' => $clinic->id,
                'entity_data' => $entityDetails,
                'onboarding_requirements' => $onboardingRequirements,
                'status' => $entityDetails['status'] ?? null,
                'type' => $entityDetails['type'] ?? null,
                'email' => $entityDetails['email'] ?? null,
                'monite_created_at' => isset($entityDetails['created_at']) ? new DateTimeImmutable($entityDetails['created_at']) : null,
                'monite_updated_at' => isset($entityDetails['updated_at']) ? new DateTimeImmutable($entityDetails['updated_at']) : null,
            ]
        );

        Log::debug('MoniteEntity record created/updated', [
            'clinic_id' => $clinic->id,
            'monite_entity_id' => $moniteEntity->monite_entity_id,
            'status' => $moniteEntity->status,
            'type' => $moniteEntity->type,
            'has_onboarding_requirements' => $onboardingRequirements !== null,
        ]);
    }

    /**
     * Enable payment methods for a Monite entity
     */
    private function enablePaymentMethods(Clinic $clinic, string $entityId): void
    {
        try {
            $paymentMethodsData = $this->preparePaymentMethodsData();

            Log::info('Enabling payment methods for Monite entity', [
                'clinic_id' => $clinic->id,
                'entity_id' => $entityId,
                'payment_methods' => $paymentMethodsData,
            ]);

            $response = $this->moniteClient
                ->withEntityId($entityId)
                ->put("/entities/{$entityId}/payment_methods", $paymentMethodsData);

            if (! $response->successful()) {
                Log::error('Failed to enable payment methods for Monite entity', [
                    'clinic_id' => $clinic->id,
                    'entity_id' => $entityId,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            $responseData = $response->json();
            Log::info('Payment methods enabled successfully for Monite entity', [
                'clinic_id' => $clinic->id,
                'entity_id' => $entityId,
                'enabled_methods' => $responseData['data'] ?? null,
            ]);

        } catch (Exception $e) {
            // Log but don't fail the entity creation if payment methods enablement fails
            Log::warning('Failed to enable payment methods for Monite entity', [
                'clinic_id' => $clinic->id,
                'entity_id' => $entityId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Prepare payment methods configuration data
     */
    private function preparePaymentMethodsData(): array
    {
        $config = config('monite.payment_methods', []);

        $paymentMethods = [];

        if (! empty($config['receive'])) {
            $paymentMethods['payment_methods_receive'] = $config['receive'];
        }

        if (! empty($config['send'])) {
            $paymentMethods['payment_methods_send'] = $config['send'];
        }

        return $paymentMethods;
    }

    /**
     * Create a mailbox for the entity to receive invoices via email.
     */
    private function createMailboxForEntity(string $entityId, Clinic $clinic): void
    {
        try {
            $domainId = config('monite.mailbox.domain_id');

            if (! $domainId) {
                Log::warning('Mailbox domain ID not configured, skipping mailbox creation', [
                    'entity_id' => $entityId,
                ]);

                return;
            }

            Log::info('Creating mailbox for Monite entity', [
                'entity_id' => $entityId,
                'clinic_id' => $clinic->id,
                'domain_id' => $domainId,
            ]);

            $mailboxData = [
                'related_object_type' => config('monite.mailbox.related_object_type', 'payable'),
                'mailbox_name' => $this->generateMailboxName($clinic),
                'mailbox_domain_id' => $domainId,
            ];

            $response = $this->moniteClient
                ->withEntityId($entityId)
                ->post('/mailboxes', $mailboxData);

            if ($response->successful()) {
                $mailboxInfo = $response->json();
                $mailboxAddress = $mailboxInfo['mailbox_full_address'] ?? null;

                // Save the mailbox address to the MoniteEntity
                if ($mailboxAddress) {
                    $moniteEntity = MoniteEntity::where('monite_entity_id', $entityId)->first();
                    if ($moniteEntity) {
                        $moniteEntity->update(['mailbox_address' => $mailboxAddress]);
                        Log::info('Mailbox address saved to MoniteEntity', [
                            'entity_id' => $entityId,
                            'mailbox_address' => $mailboxAddress,
                        ]);
                    } else {
                        Log::warning('MoniteEntity not found when saving mailbox address', [
                            'entity_id' => $entityId,
                            'mailbox_address' => $mailboxAddress,
                        ]);
                    }
                } else {
                    Log::warning('No mailbox_full_address in response', [
                        'entity_id' => $entityId,
                        'response' => $mailboxInfo,
                    ]);
                }

                Log::info('Mailbox created successfully', [
                    'entity_id' => $entityId,
                    'mailbox_id' => $mailboxInfo['id'] ?? null,
                    'mailbox_full_address' => $mailboxAddress,
                    'mailbox_name' => $mailboxInfo['mailbox_name'] ?? null,
                    'status' => $mailboxInfo['status'] ?? null,
                ]);
            } else {
                // Check if it's a duplicate error (mailbox already exists)
                $responseBody = $response->json();
                if ($response->status() === 422 &&
                    isset($responseBody['error']['code']) &&
                    $responseBody['error']['code'] === 'mailbox_already_exists') {

                    // Try to fetch existing mailbox to get the address
                    $this->fetchExistingMailboxAddress($entityId, $mailboxData['mailbox_name']);

                    Log::info('Mailbox already exists for entity', [
                        'entity_id' => $entityId,
                        'mailbox_name' => $mailboxData['mailbox_name'],
                    ]);
                } else {
                    Log::warning('Failed to create mailbox', [
                        'entity_id' => $entityId,
                        'status' => $response->status(),
                        'response' => $response->body(),
                    ]);
                }
            }
        } catch (Exception $e) {
            // Don't block entity creation if mailbox creation fails
            Log::warning('Exception creating mailbox', [
                'entity_id' => $entityId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Generate a unique mailbox name for the clinic.
     */
    private function generateMailboxName(Clinic $clinic): string
    {
        // Use clinic name to create a unique mailbox name
        return Str::slug($clinic->name).'-payables';
    }

    /**
     * Fetch existing mailbox address when mailbox already exists.
     */
    private function fetchExistingMailboxAddress(string $entityId, string $mailboxName): void
    {
        try {
            // Fetch existing mailboxes for the entity
            $response = $this->moniteClient
                ->withEntityId($entityId)
                ->get('/mailboxes');

            if ($response->successful()) {
                $mailboxes = $response->json();

                // Look for the specific mailbox by name
                foreach ($mailboxes['data'] ?? [] as $mailbox) {
                    if ($mailbox['mailbox_name'] === $mailboxName) {
                        $mailboxAddress = $mailbox['mailbox_full_address'] ?? null;

                        if ($mailboxAddress) {
                            $moniteEntity = MoniteEntity::where('monite_entity_id', $entityId)->first();
                            if ($moniteEntity) {
                                $moniteEntity->update(['mailbox_address' => $mailboxAddress]);
                                Log::info('Existing mailbox address saved to MoniteEntity', [
                                    'entity_id' => $entityId,
                                    'mailbox_address' => $mailboxAddress,
                                ]);
                            }
                        }
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            Log::warning('Failed to fetch existing mailbox address', [
                'entity_id' => $entityId,
                'mailbox_name' => $mailboxName,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
