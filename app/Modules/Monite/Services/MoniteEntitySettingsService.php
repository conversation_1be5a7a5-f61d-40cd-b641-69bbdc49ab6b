<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use Illuminate\Support\Facades\Log;

class MoniteEntitySettingsService
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteApiClientInterface $moniteClient
    ) {}

    /**
     * Update Monite entity settings with provided data
     */
    public function updateSettings(Clinic $clinic, array $settingsData, string $operation = 'update'): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'updateSettings')) {
            return;
        }

        if (! $clinic->monite_entity_id) {
            throw new MoniteApiException('Clinic must have a Monite entity before updating settings');
        }

        try {
            Log::info("Updating Monite settings for {$operation} - Request payload", [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'entity_id' => $clinic->monite_entity_id,
                'operation' => $operation,
                'payload' => $settingsData,
            ]);

            $response = $this->moniteClient
                ->withEntityId($clinic->monite_entity_id)
                ->patch('/settings', $settingsData);

            if (! $response->successful()) {
                Log::error('Monite settings update failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'clinic_name' => $clinic->name,
                    'entity_id' => $clinic->monite_entity_id,
                    'operation' => $operation,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            Log::info("Monite settings updated successfully for {$operation}", [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'entity_id' => $clinic->monite_entity_id,
                'operation' => $operation,
            ]);

        } catch (MoniteApiException $e) {
            Log::error("Failed to update Monite settings for {$operation}", [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'entity_id' => $clinic->monite_entity_id,
                'operation' => $operation,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get current Monite entity settings
     */
    public function getSettings(Clinic $clinic): array
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'getSettings')) {
            return [];
        }

        if (! $clinic->monite_entity_id) {
            throw new MoniteApiException('Clinic must have a Monite entity before retrieving settings');
        }

        try {
            Log::info('Retrieving Monite settings', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'entity_id' => $clinic->monite_entity_id,
            ]);

            $response = $this->moniteClient
                ->withEntityId($clinic->monite_entity_id)
                ->get('/settings');

            if (! $response->successful()) {
                Log::error('Monite settings retrieval failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'clinic_name' => $clinic->name,
                    'entity_id' => $clinic->monite_entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            $settings = $response->json();

            Log::info('Monite settings retrieved successfully', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'entity_id' => $clinic->monite_entity_id,
                'settings' => $settings,
            ]);

            return $settings;

        } catch (MoniteApiException $e) {
            Log::error('Failed to retrieve Monite settings', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
