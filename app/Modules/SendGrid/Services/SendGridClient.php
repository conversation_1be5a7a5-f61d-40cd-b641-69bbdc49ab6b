<?php

declare(strict_types=1);

namespace App\Modules\SendGrid\Services;

use App\Modules\SendGrid\Contracts\SendGridClientInterface;
use InvalidArgumentException;
use RuntimeException;
use SendGrid;
use SendGrid\Mail\Bcc;
use SendGrid\Mail\Cc;
use SendGrid\Mail\From;
use SendGrid\Mail\Mail;
use SendGrid\Mail\To;
use SendGrid\Response as SendGridResponse;

class SendGridClient implements SendGridClientInterface
{
    private SendGrid $client;

    public function __construct()
    {
        $apiKey = config('sendgrid.api_key');

        if (empty($apiKey)) {
            throw new RuntimeException('SendGrid API key is not configured. Please set SENDGRID_API_KEY environment variable.');
        }

        $this->client = new SendGrid($apiKey);
    }

    /**
     * Send a templated email via SendGrid.
     *
     * @param  string  $templateId  The SendGrid template ID
     * @param  array{to: array<array{email: string, name: string, template_data?: array}>, from?: array{email: string, name: string}, cc?: array<array{email: string, name: string}>, bcc?: array<array{email: string, name: string}>}  $params  Email parameters
     * @return SendGridResponse The response from SendGrid
     *
     * @throws InvalidArgumentException If required parameters are missing or invalid
     * @throws RuntimeException If sending fails
     */
    public function sendTemplatedEmail(string $templateId, array $params): SendGridResponse
    {
        if (! isset($params['to']) || ! is_array($params['to']) || empty($params['to'])) {
            throw new InvalidArgumentException('The "to" parameter is required and must be a non-empty array');
        }

        $from = $this->getFrom($params['from'] ?? []);
        $tos = [];

        foreach ($params['to'] as $to) {
            if (! isset($to['email']) || ! isset($to['name'])) {
                throw new InvalidArgumentException('Each "to" recipient must include email and name');
            }

            $tos[] = new To(
                $to['email'],
                $to['name'],
                $to['template_data'] ?? []
            );
        }

        $email = new Mail($from, $tos);

        if (isset($params['cc'])) {
            $ccs = [];
            foreach ($params['cc'] as $cc) {
                if (! isset($cc['email']) || ! isset($cc['name'])) {
                    throw new InvalidArgumentException('Each "cc" recipient must include email and name');
                }

                $ccs[] = new Cc($cc['email'], $cc['name']);
            }

            $email->addCcs($ccs);
        }

        if (isset($params['bcc'])) {
            $bccs = [];
            foreach ($params['bcc'] as $bcc) {
                if (! isset($bcc['email']) || ! isset($bcc['name'])) {
                    throw new InvalidArgumentException('Each "bcc" recipient must include email and name');
                }

                $bccs[] = new Bcc($bcc['email'], $bcc['name']);
            }

            $email->addBccs($bccs);
        }

        $email->setTemplateId($templateId);

        return $this->client->send($email);
    }

    private function getFrom(array $from = []): From
    {
        if (empty($from)) {
            return new From(config('sendgrid.from_email'), config('sendgrid.from_name'));
        }

        if (! isset($from['email']) || ! isset($from['name'])) {
            throw new InvalidArgumentException('Custom from address must include an email and name');
        }

        return new From($from['email'], $from['name']);
    }
}
