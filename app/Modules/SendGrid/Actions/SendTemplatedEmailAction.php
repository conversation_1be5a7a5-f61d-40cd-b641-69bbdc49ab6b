<?php

declare(strict_types=1);

namespace App\Modules\SendGrid\Actions;

use App\Modules\SendGrid\Contracts\SendGridClientInterface;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;

final class SendTemplatedEmailAction
{
    public function __construct(
        private readonly SendGridClientInterface $sendGridClient
    ) {}

    /**
     * Execute the action to send a templated email via SendGrid
     *
     * recipient [['email' => '<EMAIL>', 'name' => 'User Name', 'template_data' => [...]]]
     *
     * @param  array  $params  Array containing:
     *                         - to: array of recipients
     *                         - from: (optional) sender details
     *                         - cc: (optional) array of CC recipients
     *                         - bcc: (optional) array of BCC recipients
     * @return bool Returns true if email was sent successfully, false otherwise
     */
    public function handle(string $templateId, array $params): bool
    {
        try {
            $this->validateParams($params);
            $response = $this->sendGridClient->sendTemplatedEmail($templateId, $params);
        } catch (Throwable $e) {
            Log::error('Failed to send SendGrid email', [
                'template_id' => $templateId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }

        if ($response->statusCode() === 202) {
            return true;
        }

        Log::error('SendGrid email failed', [
            'template_id' => $templateId,
            'status_code' => $response->statusCode(),
            'response_body' => $response->body(),
        ]);

        return false;
    }

    /**
     * Validate required parameters
     */
    private function validateParams(array $params): void
    {
        if (! isset($params['to']) || empty($params['to'])) {
            throw new Exception('Recipients (to) parameter is required and must not be empty');
        }

        if (! is_array($params['to'])) {
            throw new Exception('Recipients (to) parameter must be an array');
        }

        foreach ($params['to'] as $recipient) {
            if (! isset($recipient['email'])) {
                throw new Exception('Each recipient must have an email address');
            }
        }
    }
}
