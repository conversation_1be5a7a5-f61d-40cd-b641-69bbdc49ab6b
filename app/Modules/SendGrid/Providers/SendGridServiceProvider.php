<?php

declare(strict_types=1);

namespace App\Modules\SendGrid\Providers;

use App\Modules\SendGrid\Contracts\SendGridClientInterface;
use App\Modules\SendGrid\Services\SendGridClient;
use Illuminate\Support\ServiceProvider;

final class SendGridServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->mergeConfigFrom(__DIR__.'/../config/sendgrid.php', 'sendgrid');

        $this->app->singleton(SendGridClientInterface::class, SendGridClient::class);
    }
}
