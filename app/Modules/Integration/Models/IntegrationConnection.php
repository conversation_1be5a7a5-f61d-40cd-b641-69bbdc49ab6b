<?php

declare(strict_types=1);

namespace App\Modules\Integration\Models;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionAlertType;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Observers\IntegrationConnectionObserver;
use Database\Factories\Modules\Integration\IntegrationConnectionFactory;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

#[ObservedBy([IntegrationConnectionObserver::class])]
final class IntegrationConnection extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    protected $attributes = [
        'status' => IntegrationConnectionStatus::Connecting,
    ];

    protected $hidden = [
        'credentials',
    ];

    public static function boot(): void
    {
        parent::boot();

        self::created(function (IntegrationConnection $connection) {
            $connection->addInfoAlert(
                '**Great news! We’re connecting you with your vendor** — this may take a few minutes. Feel free to check back in a bit if you’d like.'
            );
        });

        self::updated(function (IntegrationConnection $connection) {
            $connection->removeAlert();
        });
    }

    public static function newFactory()
    {
        return IntegrationConnectionFactory::new();
    }

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function alert(): HasOne
    {
        return $this->hasOne(IntegrationConnectionAlert::class);
    }

    public function sessions(): HasMany
    {
        return $this->hasMany(IntegrationSession::class);
    }

    public function markAsDisconnected(string $message): void
    {
        $this->getConnection()->transaction(function () use ($message) {
            $this->update([
                'status' => IntegrationConnectionStatus::Disconnected,
            ]);

            $this->addErrorAlert($message);
        });
    }

    public function addInfoAlert(string $message): void
    {
        $this->alert()->updateOrCreate([], [
            'type' => IntegrationConnectionAlertType::Info,
            'message' => $message,
        ]);
    }

    public function addErrorAlert(string $message): void
    {
        $this->alert()->updateOrCreate([], [
            'type' => IntegrationConnectionAlertType::Error,
            'message' => $message,
        ]);
    }

    public function removeAlert(): void
    {
        $this->alert()->delete();
    }

    protected function casts(): array
    {
        return [
            'credentials' => 'encrypted:array',
            'status' => IntegrationConnectionStatus::class,
            'last_sync_at' => 'datetime',
        ];
    }
}
