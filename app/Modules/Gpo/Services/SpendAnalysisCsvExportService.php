<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Services;

use App\Modules\Gpo\Http\Requests\SpendAnalysisRequest;
use Brick\Money\Money;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class SpendAnalysisCsvExportService
{
    public function __construct(
        private readonly SpendAnalysisService $spendAnalysisService
    ) {}

    public function exportToCsv(string $gpoAccountId, SpendAnalysisRequest $request): StreamedResponse
    {
        $fileName = $this->generateFileName();

        $callback = function () use ($gpoAccountId, $request) {
            $file = fopen('php://output', 'w');

            $this->writeCsvHeaders($file);
            $this->writeCsvData($file, $gpoAccountId, $request);

            fclose($file);
        };

        return response()->streamDownload($callback, $fileName, [
            'Content-Type' => 'text/csv',
        ]);
    }

    private function generateFileName(): string
    {
        return 'gpo_spend_analysis_'.now()->format('Y-m-d_H-i-s').'.csv';
    }

    private function writeCsvHeaders($file): void
    {
        fputcsv($file, [
            'Member ID',
            'Clinic Name',
            'GPO Member Since',
            'HF Member Since',
            'Status',
            'Fulltime DVM',
            'Total Exam Rooms',
            'Total Spend',
            // 'Total Spend YOY %',
            'Rebates Earned',
            // 'Rebates Earned YOY %',
            'Preferred Share Amount',
            'Preferred Share %',
            // 'Preferred Share YOY %',
            'Preferred Vendor Spend Share Amount',
            'Preferred Vendor Spend Share Total',
            'Preferred Vendor Spend Share %',
            // 'Preferred Vendor Spend Share YOY %',
            'Annual Budget',
            'Previous Year Spend',
            'Preferred Vendor %',
            'Non-Preferred Vendor %',
            'GPO Participation Rate %',
            // 'GPO Participation Rate YOY %',
            'Total Orders',
            'Active Users',
        ]);
    }

    private function writeCsvData($file, string $gpoAccountId, SpendAnalysisRequest $request): void
    {
        // Process data in chunks to avoid memory issues
        $this->spendAnalysisService
            ->getClinicsForExport($gpoAccountId, $request)
            ->chunk(1000)
            ->each(function ($rows) use ($file) {
                foreach ($rows as $row) {
                    $this->writeCsvRow($file, $row);
                }
            });
    }

    private function writeCsvRow($file, array $data): void
    {
        fputcsv($file, [
            $data['gpo_membership_reference'] ?? '-',
            $data['name'],
            $data['gpo_member_since'] ?? '-',
            $data['hf_member_since'],
            $data['status'],
            $data['fulltime_dvm'],
            $data['total_exam_rooms'],
            $this->formatMoneyAmount($data['total_spend']['amount'] ?? '0.00'),
            // $data['total_spend']['yoy_percentage'].'%',
            $this->formatMoneyAmount($data['rebates_earned']['amount'] ?? '0.00'),
            // $data['rebates_earned']['yoy_percentage'].'%',
            $this->formatMoneyAmount($data['preferred_share']['amount'] ?? '0.00'),
            $data['preferred_share']['percentage'].'%',
            // $data['preferred_share']['yoy_percentage'].'%',
            $this->formatMoneyAmount($data['preferred_vendor_spend_share']['amount'] ?? '0.00'),
            $this->formatMoneyAmount($data['preferred_vendor_spend_share']['total'] ?? '0.00'),
            $data['preferred_vendor_spend_share']['percentage'].'%',
            // $data['preferred_vendor_spend_share']['yoy_percentage'].'%',
            $this->formatMoneyAmount($data['spend']['annual_budget'] ?? '0.00'),
            // $this->formatMoneyAmount($data['spend']['previous_year_spend'] ?? '0.00'),
            $data['spend']['preferred_vendor_percentage'].'%',
            $data['spend']['non_preferred_vendor_percentage'].'%',
            $data['market_share_analysis']['gpo_vendor_participation_rate']['percentage'] ?? '0.00%',
            $data['market_share_analysis']['gpo_vendor_participation_rate']['yoy_percentage'] ?? '0.00%',
            $data['total_orders'],
            $data['active_users'],
        ]);
    }

    private function formatMoneyAmount($amount): string
    {
        if (is_null($amount)) {
            return Money::ofMinor(0, 'USD')->formatTo('en_US');
        }

        $minorUnits = (int) round((float) (string) $amount * 100);

        return Money::ofMinor($minorUnits, 'USD')->formatTo('en_US');
    }
}
