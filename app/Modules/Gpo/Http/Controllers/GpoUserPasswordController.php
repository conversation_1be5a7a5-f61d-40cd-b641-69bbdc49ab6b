<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Controllers;

use App\Actions\ResetUserPassword;
use App\Http\Controllers\Controller;
use App\Http\Resources\GpoUser;
use App\Modules\Gpo\Http\Requests\ResetGpoPasswordRequest;
use Illuminate\Http\JsonResponse;

final class GpoUserPasswordController extends Controller
{
    public function update(
        ResetGpoPasswordRequest $request,
        ResetUserPassword $action
    ): JsonResponse {
        $user = $action->handle($request->validated(), 'gpo_users');

        return GpoUser::make($user)->response();
    }
}
