<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Controllers;

use App\Actions\SendPasswordResetLink;
use App\Http\Controllers\Controller;
use App\Modules\Gpo\Http\Requests\GpoPasswordResetRequest;
use App\Modules\Gpo\Models\GpoUser;
use Illuminate\Http\Response;

final class GpoPasswordResetController extends Controller
{
    public function store(
        GpoPasswordResetRequest $request,
        SendPasswordResetLink $action
    ): Response {
        if (! GpoUser::where('email', $request->validated('email'))->exists()) {
            return response()->noContent();
        }

        $action->handle($request->validated(), 'gpo_users');

        return response()->noContent();
    }
}
