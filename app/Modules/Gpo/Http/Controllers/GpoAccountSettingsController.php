<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Gpo\Http\Requests\StoreGpoAccountSettingRequest;
use App\Modules\Gpo\Http\Requests\UpdateGpoAccountSettingRequest;
use App\Modules\Gpo\Http\Resources\GpoAccountSettingResource;
use App\Modules\Gpo\Models\GpoAccountSettings;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

final class GpoAccountSettingsController extends Controller
{
    public function index(Request $request): AnonymousResourceCollection
    {
        $user = $request->user('gpo');

        $settings = GpoAccountSettings::query()
            ->where('gpo_account_id', $user->account_id)
            ->get();

        return GpoAccountSettingResource::collection($settings);
    }

    public function store(StoreGpoAccountSettingRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $setting = GpoAccountSettings::create([
            'gpo_account_id' => $request->user('gpo')->account_id,
            'setting_key' => $validated['setting_key'],
            'value' => $validated['value'],
        ]);

        return GpoAccountSettingResource::make($setting)
            ->response()
            ->setStatusCode(JsonResponse::HTTP_CREATED);
    }

    public function show(Request $request, string $gpoAccountSettings): JsonResponse
    {
        $user = $request->user('gpo');
        $setting = GpoAccountSettings::findOrFail($gpoAccountSettings);

        if ($setting->gpo_account_id !== $user->account_id) {
            abort(JsonResponse::HTTP_FORBIDDEN, 'You do not have permission to view this setting.');
        }

        return GpoAccountSettingResource::make($setting)
            ->response();
    }

    public function update(
        UpdateGpoAccountSettingRequest $request,
        string $gpoAccountSettings
    ): JsonResponse {
        $user = $request->user('gpo');
        $setting = GpoAccountSettings::findOrFail($gpoAccountSettings);

        if ($setting->gpo_account_id !== $user->account_id) {
            abort(JsonResponse::HTTP_FORBIDDEN, 'You do not have permission to update this setting.');
        }

        $validated = $request->validated();

        if ($validated['setting_key'] !== $setting->setting_key->value) {
            $isUnique = GpoAccountSettings::query()
                ->where('gpo_account_id', $user->account_id)
                ->where('setting_key', $validated['setting_key'])
                ->doesntExist();

            if (! $isUnique) {
                abort(JsonResponse::HTTP_UNPROCESSABLE_ENTITY, 'Setting key must be unique.');
            }
        }

        $setting->update([
            'setting_key' => $validated['setting_key'] ?? $setting->setting_key,
            'value' => $validated['value'] ?? $setting->value,
        ]);

        return GpoAccountSettingResource::make($setting)
            ->response();
    }

    public function destroy(Request $request, string $gpoAccountSettings): JsonResponse
    {
        $user = $request->user('gpo');
        $setting = GpoAccountSettings::findOrFail($gpoAccountSettings);

        if ($setting->gpo_account_id !== $user->account_id) {
            abort(JsonResponse::HTTP_FORBIDDEN, 'You do not have permission to delete this setting.');
        }

        $setting->delete();

        return response()->json(null, JsonResponse::HTTP_NO_CONTENT);
    }
}
