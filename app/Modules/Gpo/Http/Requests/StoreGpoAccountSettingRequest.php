<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Requests;

use App\Modules\Gpo\Enums\GpoSettingKey;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class StoreGpoAccountSettingRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'setting_key' => [
                'required',
                'string',
                Rule::enum(GpoSettingKey::class),
                Rule::unique('gpo_account_settings')
                    ->where('gpo_account_id', $this->user('gpo')->account_id),
            ],
            'value' => ['required', 'array'],
        ];
    }

    public function messages(): array
    {
        return [
            'setting_key.required' => 'The setting key is required.',
            'setting_key.enum' => 'The setting key must be a valid setting type.',
            'setting_key.unique' => 'A setting with this key already exists for your account.',
            'value.required' => 'The value is required.',
            'value.array' => 'The value must be an array.',
        ];
    }
}
