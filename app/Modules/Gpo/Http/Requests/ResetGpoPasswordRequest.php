<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Requests;

use App\Modules\Gpo\Models\GpoUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\Password;

final class ResetGpoPasswordRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'token' => ['required'],
            'email' => ['required', 'string', 'lowercase', 'email', new Exists(GpoUser::class, 'email')],
            'password' => ['required', 'string', Password::defaults()],
        ];
    }
}
