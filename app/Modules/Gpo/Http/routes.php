<?php

declare(strict_types=1);

use App\Modules\Gpo\Http\Controllers\AuthController;
use App\Modules\Gpo\Http\Controllers\GpoAccountSettingsController;
use App\Modules\Gpo\Http\Controllers\GpoPasswordResetController;
use App\Modules\Gpo\Http\Controllers\GpoUserPasswordController;
use App\Modules\Gpo\Http\Controllers\PlatformUsageController;
use App\Modules\Gpo\Http\Controllers\SpendAnalysisController;
use App\Modules\Gpo\Http\Controllers\VendorsOverviewController;
use Illuminate\Support\Facades\Route;

Route::prefix('api/gpo')->group(function () {
    Route::post('login', [AuthController::class, 'login']);

    Route::post('password-resets', [GpoPasswordResetController::class, 'store']);
    Route::patch('users/me/password', [GpoUserPasswordController::class, 'update']);

    Route::middleware('auth:gpo')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('user', [AuthController::class, 'user']);

        Route::group(['prefix' => 'account-settings'], function () {
            Route::get('/', [GpoAccountSettingsController::class, 'index']);
            Route::post('/', [GpoAccountSettingsController::class, 'store']);
            Route::get('/{gpoAccountSettings}', [GpoAccountSettingsController::class, 'show']);
            Route::patch('/{gpoAccountSettings}', [GpoAccountSettingsController::class, 'update']);
            Route::put('/{gpoAccountSettings}', [GpoAccountSettingsController::class, 'update']);
            Route::delete('/{gpoAccountSettings}', [GpoAccountSettingsController::class, 'destroy']);
        });

        Route::group(['prefix' => 'spend-analysis'], function () {
            Route::get('/', [SpendAnalysisController::class, 'index']);
            Route::get('/export', [SpendAnalysisController::class, 'export']);
            Route::get('/summary', [SpendAnalysisController::class, 'summary']);
        });

        Route::group(['prefix' => 'vendors-overview'], function () {
            Route::get('/', [VendorsOverviewController::class, 'index']);
            Route::get('/export', [VendorsOverviewController::class, 'export']);
        });

        Route::group(['prefix' => 'platform-usage'], function () {
            Route::get('/summary', [PlatformUsageController::class, 'summary']);
            Route::get('/export', [PlatformUsageController::class, 'export']);
        });
    });
});
