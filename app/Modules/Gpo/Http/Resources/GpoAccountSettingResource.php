<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class GpoAccountSettingResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $value = $this->value;

        // Convert goal_amount from cents to dollars for VendorGoals settings
        $settingKeyValue = is_object($this->setting_key) ? $this->setting_key->value : $this->setting_key;

        // Check if this is vendor_goals setting and convert goal_amount from cents to dollars
        if ($settingKeyValue === 'vendor_goals' && isset($value['goals']) && is_array($value['goals'])) {
            foreach ($value['goals'] as &$goal) {
                if (isset($goal['goal_amount']) && is_numeric($goal['goal_amount'])) {
                    $goal['goal_amount'] = $goal['goal_amount'] / 100;
                }
            }
        }

        return [
            'id' => $this->id,
            'gpo_account_id' => $this->gpo_account_id,
            'setting_key' => $this->setting_key,
            'value' => $value,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
