<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Data;

use App\Models\Vendor;
use Brick\Money\Money;
use Spatie\LaravelData\Data;

final class VendorOverviewData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly string $type,
        public readonly ?string $imageUrl,
        public readonly float $marketSharePercentage,
        public readonly float $totalSpend,
        public readonly float $growthTargetPercentage,
        public readonly float $amountUntilGoal,
        public readonly ?float $goalAmount,
        public readonly ?string $notification,
    ) {}

    public static function fromModel(
        Vendor $vendor,
        float $marketSharePercentage,
        int $totalSpend,
        float $growthTargetPercentage,
        int $amountUntilGoal,
        ?int $goalAmount,
        ?string $notification = null
    ): self {
        return new self(
            id: $vendor->id,
            name: $vendor->name,
            type: $vendor->type->value,
            imageUrl: $vendor->image_path ? asset("storage/{$vendor->image_path}") : null,
            marketSharePercentage: $marketSharePercentage,
            totalSpend: Money::ofMinor($totalSpend, 'USD')->getAmount()->toFloat(),
            growthTargetPercentage: $growthTargetPercentage,
            amountUntilGoal: Money::ofMinor($amountUntilGoal, 'USD')->getAmount()->toFloat(),
            goalAmount: $goalAmount ? (float) $goalAmount / 100 : null,
            notification: $notification,
        );
    }
}
