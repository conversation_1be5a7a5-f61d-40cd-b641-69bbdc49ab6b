<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Models;

use App\Modules\Gpo\Casts\GpoSettingValueCast;
use App\Modules\Gpo\Enums\GpoSettingKey;
use App\Modules\Gpo\Models\Factories\GpoAccountSettingsFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class GpoAccountSettings extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'gpo_account_id',
        'setting_key',
        'value',
    ];

    protected $casts = [
        'setting_key' => GpoSettingKey::class,
        'value' => GpoSettingValueCast::class,
    ];

    public static function newFactory(): GpoAccountSettingsFactory
    {
        return GpoAccountSettingsFactory::new();
    }

    public function gpo(): BelongsTo
    {
        return $this->belongsTo(GpoAccount::class, 'gpo_account_id');
    }
}
