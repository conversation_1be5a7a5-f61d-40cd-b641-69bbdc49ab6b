<?php

declare(strict_types=1);

namespace App\Support;

final class StateNormalizer
{
    /**
     * US state name to abbreviation mapping
     */
    private const STATE_MAP = [
        'alabama' => 'AL',
        'alaska' => 'AK',
        'arizona' => 'AZ',
        'arkansas' => 'AR',
        'california' => 'CA',
        'colorado' => 'CO',
        'connecticut' => 'CT',
        'delaware' => 'DE',
        'florida' => 'FL',
        'georgia' => 'GA',
        'hawaii' => 'HI',
        'idaho' => 'ID',
        'illinois' => 'IL',
        'indiana' => 'IN',
        'iowa' => 'IA',
        'kansas' => 'KS',
        'kentucky' => 'KY',
        'louisiana' => 'LA',
        'maine' => 'ME',
        'maryland' => 'MD',
        'massachusetts' => 'MA',
        'michigan' => 'MI',
        'minnesota' => 'MN',
        'mississippi' => 'MS',
        'missouri' => 'MO',
        'montana' => 'MT',
        'nebraska' => 'NE',
        'nevada' => 'NV',
        'new hampshire' => 'NH',
        'new jersey' => 'NJ',
        'new mexico' => 'NM',
        'new york' => 'NY',
        'north carolina' => 'NC',
        'north dakota' => 'ND',
        'ohio' => 'OH',
        'oklahoma' => 'OK',
        'oregon' => 'OR',
        'pennsylvania' => 'PA',
        'rhode island' => 'RI',
        'south carolina' => 'SC',
        'south dakota' => 'SD',
        'tennessee' => 'TN',
        'texas' => 'TX',
        'utah' => 'UT',
        'vermont' => 'VT',
        'virginia' => 'VA',
        'washington' => 'WA',
        'west virginia' => 'WV',
        'wisconsin' => 'WI',
        'wyoming' => 'WY',
        'district of columbia' => 'DC',
        // Territories
        'puerto rico' => 'PR',
        'guam' => 'GU',
        'virgin islands' => 'VI',
        'american samoa' => 'AS',
        'northern mariana islands' => 'MP',
    ];

    /**
     * Normalize a US state name or abbreviation to its 2-letter abbreviation
     */
    public static function normalize(?string $state): ?string
    {
        if ($state === null || trim($state) === '') {
            return null;
        }

        $state = trim($state);

        // If it's already a 2-letter code, return it uppercase
        if (mb_strlen($state) === 2) {
            $upperState = mb_strtoupper($state);
            // Validate it's a known abbreviation
            if (in_array($upperState, self::STATE_MAP, true)) {
                return $upperState;
            }
        }

        // Try to match the full state name
        $lowercaseState = mb_strtolower($state);
        if (isset(self::STATE_MAP[$lowercaseState])) {
            return self::STATE_MAP[$lowercaseState];
        }

        // Return original if we can't normalize
        return $state;
    }

    /**
     * Check if a state abbreviation is valid
     */
    public static function isValid(?string $state): bool
    {
        if ($state === null || trim($state) === '') {
            return false;
        }

        $normalized = self::normalize($state);

        return $normalized !== null && in_array($normalized, self::STATE_MAP, true);
    }
}
