<?php

declare(strict_types=1);

namespace App\Support;

final class PhoneNumberFormatter
{
    /**
     * Format a US phone number to E.164 format for international APIs
     *
     * @param  string  $phoneNumber  The phone number in various formats (e.g., "(*************", "************", "6166626230")
     * @return string|null The formatted phone number in E.164 format (e.g., "+16166626230") or null if invalid
     */
    public static function formatToE164(string $phoneNumber): ?string
    {
        // Remove all non-digit characters
        $digits = preg_replace('/\D/', '', $phoneNumber);

        // Handle different US phone number formats
        if (mb_strlen($digits) === 10) {
            // Standard 10-digit US number: 6166626230 -> +16166626230
            return '+1'.$digits;
        }

        if (mb_strlen($digits) === 11 && str_starts_with($digits, '1')) {
            // 11-digit number starting with 1: 16166626230 -> +16166626230
            return '+'.$digits;
        }

        if (str_starts_with($digits, '+1') && mb_strlen($digits) === 12) {
            // Already in E.164 format: +16166626230
            return $digits;
        }

        // Check for invalid lengths (more than 11 digits without +1 prefix)
        if (mb_strlen($digits) > 11) {
            return null;
        }

        // Invalid format
        return null;
    }

    /**
     * Validate if a phone number can be formatted to E.164
     *
     * @param  string  $phoneNumber  The phone number to validate
     * @return bool True if the phone number can be formatted to E.164
     */
    public static function isValidUSPhoneNumber(string $phoneNumber): bool
    {
        return self::formatToE164($phoneNumber) !== null;
    }
}
