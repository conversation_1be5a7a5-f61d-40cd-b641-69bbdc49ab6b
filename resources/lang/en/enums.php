<?php

declare(strict_types=1);

return [
    /**
     * Account Roles
     */
    'Administrator' => 'Administrator',
    'Manager' => 'Manager',
    'Purchaser' => 'Purchaser',

    /**
     * Address Types
     */
    'Billing' => 'Billing',
    'Shipping' => 'Shipping',

    /**
     * Clinic Budget Type
     */
    'Dynamic' => 'Dynamic',
    'Static' => 'Static',

    /**
     * Expense Categories
     */
    'COGS' => 'Cost of Goods Sold (COGS)',
    'GA' => 'General and Administrative (GA)',

    /**
     * Import Task Data Types
     */
    'ClinicPrices' => 'Clinic Prices',
    'Orders' => 'Orders',
    'Products' => 'Products',

    /**
     * Import Task Statuses
     */
    'Pending' => 'Pending',
    'Processing' => 'Processing',
    'CompletedWithErrors' => 'Completed With Errors',
    'Completed' => 'Completed',
    'Failed' => 'Failed',

    /**
     * Import Task Record Statuses
     */
    'Pending' => 'Pending',
    'Processing' => 'Processing',
    'Completed' => 'Completed',
    'Failed' => 'Failed',

    /**
     * Order Statuses
     */
    'Placed' => 'Placed',
    'Processed' => 'Processed',
    'Shipped' => 'Shipped',
    'PartiallyDelivered' => 'Partially Delivered',
    'Delivered' => 'Delivered',
    'Backordered' => 'Backordered',
    'PartiallyShipped' => 'Partially Shipped',
    'Cancelled' => 'Cancelled',
    'Returned' => 'Returned',
    'Accepted' => 'Accepted',
    'PlacementFailed' => 'Placement Failed',
    'Rejected' => 'Rejected',

    /**
     * Account Roles
     */
    'Admin' => 'Admin',
    'Member' => 'Member',
    'Owner' => 'Owner',

    /**
     * Payment Methods
     */
    'Invoice' => 'Invoice',

    /**
     * Product Stock Status
     */
    'DropShip' => 'Drop Ship',
    'InStock' => 'In Stock',
    'OutOfStock' => 'Out of Stock',
    'SpecialOrder' => 'Special Order',
    'Discontinued' => 'Discontinued',

    /**
     * Product Sync Status
     */
    'Completed' => 'Completed',
    'Failed' => 'Failed',
    'InProgress' => 'In Progress',
    'Pending' => 'Pending',

    /**
     * Smart Cart Template Status
     */
    'Draft' => 'Draft',
    'Ready' => 'Ready',

    /**
     * Vendor Connection Status
     */
    'NotConnected' => 'Not Connected',
    'Connecting' => 'Connecting',
    'Connected' => 'Connected',
    'Error' => 'Error',

    /**
     * Vendor Types
     */
    'Distributor' => 'Distributor',
    'Manufacturer' => 'Manufacturer',

    /**
     * Vendor Authentication Kinds
     */
    'Basic' => 'Basic',
    'OAuth2' => 'OAuth2',

    /**
     * Sync Frequency
     */
    'Daily' => 'Daily',
    'Weekly' => 'Weekly',
    'Monthly' => 'Monthly',
];
