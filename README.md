# HighFive Local Development Environment 🚀

Welcome to HighFive Veterinary Clinic Management System! This guide will help you set up the complete development environment in just a few minutes. This all-in-one Docker setup gets you coding fast with both the Laravel backend and React frontend.

---

## 🚀 Quick Start (One-Command Setup)

**NEW!** We've created a comprehensive setup script that automates everything! 

```bash
# Clone the dev environment
<NAME_EMAIL>:highfive-vet/highfive-dev.git
cd highfive-dev

# Run the complete setup (handles everything automatically!)
./setup-complete.sh
```

This script will:
- ✅ Check all prerequisites (Docker, Git, Node.js)
- ✅ Verify GitHub SSH access
- ✅ Set up local hosts entries
- ✅ Clone all required repositories
- ✅ Configure environment files
- ✅ Start Docker services and install dependencies
- ✅ Set up frontend dependencies
- ✅ Verify everything is working
- ✅ Provide next steps and useful commands

**💡 Pro Tip:** This is the recommended approach for new developers!

---

## 📋 Prerequisites

Before you start, make sure you have these installed on your machine:

### Required Software
- **Docker Desktop** (4.0+) - [Download here](https://www.docker.com/products/docker-desktop/)
- **Git** - [Download here](https://git-scm.com/downloads)
- **Node.js & npm** (18+) - [Download here](https://nodejs.org/) *(for frontend development)*

### Verify Installation
```bash
# Check if everything is installed
docker --version          # Should show Docker version 20.10+
docker-compose --version  # Should show docker-compose version 2.0+
git --version             # Should show git version
node --version            # Should show node version 18+
npm --version             # Should show npm version
```

---

## 🏗️ Project Structure

HighFive consists of three main repositories that work together:

```
your-workspace/
├── highfive-backend/         # Laravel API & Admin
├── clinic-portal-web-app/    # React Frontend (Clinic Portal)
└── highfive-dev/             # Docker Development Environment
```

**💡 Pro Tip:** You only need to clone `highfive-dev` manually. The setup script will automatically clone the other repositories if they don't exist!

---

## 🛠️ Setup Script Features

The `setup-complete.sh` script provides a comprehensive, automated setup experience:

### **🔍 Prerequisites Check**
- Docker Desktop installation and status
- Git installation and SSH access to GitHub
- Node.js and npm installation
- System requirements validation

### **🌐 Environment Setup**
- Automatic hosts file configuration
- Environment file creation from templates
- Port configuration validation
- Service accessibility verification

### **📦 Repository Management**
- Smart cloning (only clones missing repositories)
- SSH authentication verification
- Repository structure validation
- Automatic workspace organization

### **🐳 Docker & Dependencies**
- Complete Docker service orchestration
- PHP dependencies installation
- Frontend package management
- Service health monitoring

### **✅ Verification & Testing**
- Service health checks
- Endpoint accessibility testing
- Dependency installation verification
- Comprehensive status reporting

### **📚 Developer Guidance**
- Clear next steps instructions
- Useful command references
- Service URL documentation
- Troubleshooting guidance

---

## 🧪 Testing the Setup

We've created a comprehensive test script that validates the entire setup process:

```bash
# Run the complete setup test
./test-setup.sh
```

This test script will:
- ✅ **Test all prerequisites** (Docker, Git, Node.js, npm)
- ✅ **Verify GitHub SSH access**
- ✅ **Test repository cloning** (all three repositories)
- ✅ **Validate Docker setup** (build, start, health checks)
- ✅ **Test backend setup** (Laravel application)
- ✅ **Test frontend setup** (npm dependencies)
- ✅ **Test database operations** (import/export)
- ✅ **Verify service accessibility** (all endpoints)
- ✅ **Provide detailed test results** with pass/fail status

**💡 Use this script to:**
- Validate your development environment
- Test the setup process before deploying to production
- Troubleshoot setup issues
- Ensure all components are working correctly

---

## 📋 Manual Setup (Step-by-Step)

### Step 1: Clone the Repositories

First, create a workspace directory and clone the dev environment repository:

```bash
# Create a workspace directory
mkdir highfive-workspace
cd highfive-workspace

# Clone the dev environment repository
<NAME_EMAIL>:highfive-vet/highfive-dev.git

# The setup script will automatically clone the other required repositories
```

**Note:** The setup script will automatically clone the required repositories if they don't exist:
- `highfive-backend` - Laravel API & Admin
- `clinic-portal-web-app` - React Frontend (Clinic Portal)

**💡 Smart Cloning:** If the repositories already exist, the setup script will skip cloning and use the existing ones. This means you can run `./dev setup` multiple times safely.

### Step 2: Set Up Local Hosts

Add these entries to your `/etc/hosts` file (required for local development):

```bash
# On macOS/Linux, edit with:
sudo nano /etc/hosts

# On Windows, edit:
# C:\Windows\System32\drivers\etc\hosts

# Add these lines:
127.0.0.1   admin.highfive.local
127.0.0.1   database
127.0.0.1   redis
127.0.0.1   opensearch
127.0.0.1   mailpit
```

### Step 3: One-Command Setup

Navigate to the dev environment and run the magical setup command:

```bash
cd highfive-dev
./dev setup
```

This single command will:
- ✅ **Automatically clone required repositories** (if they don't exist)
- ✅ Build and start all Docker containers
- ✅ Install PHP dependencies with Composer
- ✅ Set up the Laravel application
- ✅ Run database migrations
- ✅ Seed the database with demo data
- ✅ Install frontend dependencies (npm install)
- ✅ Set up email testing with Mailpit
- ✅ Create environment configuration from template

**Note:** The setup script now handles everything automatically, including cloning repositories and installing frontend dependencies!

**Grab a coffee ☕ - this takes about 3-5 minutes on first run!**

### Step 4: Start Frontend Development

After the setup is complete, you can start the frontend applications:

```bash
# Navigate to the frontend directory
cd ../clinic-portal-web-app

# Start Clinic Portal (Shop)
npm run start          # Runs at http://localhost:3000

# In another terminal, start GPO Portal
npm run start:gpo      # Runs at http://localhost:3001
```

### Environment Configuration

The setup automatically creates a `.env` file from the `env.example` template. You can customize the configuration by editing the `.env` file:

```bash
# Edit environment configuration
nano .env

# Key configuration options:
# - APP_PORT: Backend application port (default: 80)
# - DB_PORT: Database port (default: 5432)
# - REDIS_PORT: Redis port (default: 6379)
# - OPENSEARCH_PORT: Search engine port (default: 9200)
```

### Step 5: Verify Everything Works

Once setup is complete, visit these URLs to make sure everything is running:

| Service | URL | What You'll See |
|---------|-----|-----------------|
| 🔐 **Admin Dashboard** | http://admin.highfive.local/nova | Laravel Nova admin (login: <EMAIL> / password) |
| 📊 **Queue Monitor** | http://admin.highfive.local/horizon | Laravel Horizon dashboard |
| 📧 **Email Testing** | http://localhost:8025 | Mailpit email interface |
| 🔍 **Search Engine** | http://localhost:9200 | OpenSearch API |
| 📈 **Search Dashboard** | http://localhost:5601 | OpenSearch visual interface |

---

## 🧩 Architecture Overview

Here's what's running under the hood:

| Service | Port | Description |
|---------|------|-------------|
| **Laravel Backend** | 80* | API server with Nova admin interface |
| **PostgreSQL** | 5432 | Primary database |
| **Redis** | 6379 | Cache and queue storage |
| **OpenSearch** | 9200 | Search engine with phonetic analysis |
| **OpenSearch Dashboards** | 5601 | Search data visualization |
| **Mailpit** | 8025 | Email testing and debugging |

*\*The backend port can be configured via `APP_PORT` environment variable (default: 80). Services use host-based routing (admin.highfive.local) rather than port-based access.*

### Frontend Development

The React frontend applications run on your host machine for better development experience:
- **Clinic Portal** - Main interface for veterinary clinics (runs on http://localhost:3000)
- **GPO Portal** - Group Purchasing Organization management interface (runs on http://localhost:3001)

---

## 🛠️ Daily Development Commands

The `./dev` script provides everything you need for daily development. Run `./dev` or `./dev help` anytime for a complete list of all available commands!

### 🏃‍♂️ Essential Commands

```bash
# Start your day
./dev up                  # Start all services
./dev up --build         # Start all services with rebuild
./dev health             # Check all services are healthy
./dev check-hosts        # Verify hosts file configuration

# During development
./dev logs               # Watch all service logs
./dev test               # Run the test suite
./dev shell              # Access the app container
./dev open-all           # Open all services in browser

# End your day
./dev down               # Stop all services
```

### 🗄️ Database Commands

```bash
./dev migrate            # Run new migrations
./dev seed               # Seed with fresh test data
./dev demo               # Install demo data
./dev backup             # Backup database
./dev restore <file>     # Restore from backup
./dev db:wip             # Wipe database clean
./dev db:import          # Import database dump (local file or external)
```

### 🔍 Search & Indexing

```bash
./dev opensearch-reindex    # Rebuild search indices
./dev opensearch-health     # Check search engine status
./dev opensearch-indices    # List all search indices
./dev opensearch-plugins    # List installed OpenSearch plugins
./dev opensearch-dashboards # Open search dashboard in browser
```

### 🔧 Development Tools

```bash
./dev cache-clear        # Clear all application caches
./dev optimize          # Optimize for development
./dev queue             # Process background jobs
./dev composer-install  # Install/update PHP packages
./dev composer-update   # Update PHP dependencies
```

### 📊 Monitoring & Debugging

```bash
./dev health            # Check all service health
./dev status            # Show service status
./dev logs-app          # Backend application logs
./dev logs-db           # Database logs
./dev logs-redis        # Cache/queue logs
./dev logs-opensearch   # Search engine logs
./dev logs-dashboards   # OpenSearch Dashboards logs
./dev logs-horizon      # Queue worker logs
```

### 🧪 Testing Commands

```bash
./dev test              # Run all tests
./dev precommit         # Run pre-commit checks (composer install, pint, tests)
```

### 🚨 Troubleshooting Commands

```bash
./dev restart           # Restart all services
./dev reset             # ⚠️  Nuclear option: wipe everything and start fresh
```

### 🎯 Additional Commands Available

The commands above cover daily development needs. For a complete list including advanced commands, run:

```bash
./dev                   # Shows full help with all available commands
```

### 💡 Argument Flexibility

All `./dev` commands automatically pass through additional arguments to the underlying tools, making them incredibly flexible:

**Note:** The `--remove-orphans` flag for `./dev up` works by first running `docker-compose down --remove-orphans` to clean up orphan containers, then starting the services normally.

```bash
# Docker Compose arguments
./dev up --build                    # Rebuild containers before starting
./dev up --remove-orphans          # Remove orphan containers before starting
./dev up --build --remove-orphans  # Rebuild and remove orphans
./dev down --volumes --remove-orphans
./dev restart app database

# Logging with options
./dev logs --tail=100 -f
./dev logs-app --tail=50 --since="1h"

# Laravel Artisan commands
./dev artisan list                    # List all commands
./dev artisan make:controller User    # Create controller
./dev artisan migrate:status
./dev artisan route:list
./dev artisan tinker

# Laravel Artisan arguments
./dev migrate --force
./dev test --filter=UserTest --parallel
./dev seed --class=UserSeeder

# Composer arguments
./dev composer-install --no-dev --optimize-autoloader
./dev composer-update --with-dependencies

# Database operations
./dev backup --format=custom --verbose
./dev restore backup.sql --single-transaction

# Shell with commands
./dev shell -- ls -la
./dev shell-db -- psql -c "SELECT version();"
```

This flexibility means you can use any Docker Compose, Laravel Artisan, Composer, or PostgreSQL flags without learning new syntax!

### 🔧 Key Features

- **Host Configuration Check**: `./dev check-hosts` - Verify your hosts file is properly configured
- **Service Health Monitoring**: `./dev health` - Comprehensive health check of all services
- **Browser Integration**: `./dev open-all` - Open all services in your default browser
- **Container Rebuild**: `./dev up --build` - Rebuild containers when starting services
- **Database Import**: `./dev db:import` - Import data from local files or external databases
- **Argument Flexibility**: All commands automatically pass through additional arguments to underlying tools
- **Pre-commit Checks**: `./dev precommit` - Run comprehensive checks before committing

---

## 🧑‍💻 Typical Development Workflow

Here's how most developers use HighFive day-to-day:

### First Time Setup
```bash
# Clone and setup (one-time only)
mkdir highfive-workspace
cd highfive-workspace
<NAME_EMAIL>:highfive-vet/highfive-dev.git
cd highfive-dev
./dev setup  # This handles everything automatically!
```

### Daily Development
```bash
# Morning: Start your environment
cd highfive-dev
./dev up

# Check everything is healthy
./dev health

# Start frontend development (in separate terminals)
cd ../clinic-portal-web-app
npm run start          # Clinic Portal at http://localhost:3000
npm run start:gpo      # GPO Portal at http://localhost:3001

# Start coding! 
# - Edit backend files in highfive-backend/ (auto-reloads via Docker)
# - Edit frontend files in clinic-portal-web-app/ (hot-reload via Vite)
# - Check backend logs if needed: ./dev logs

# Run tests before committing
./dev test              # Backend tests
./dev precommit         # Run all pre-commit checks
cd ../clinic-portal-web-app && npm test  # Frontend tests

# Evening: Stop everything
./dev down
```

---

## 🗄️ Database Import & Testing

The development environment includes powerful database import capabilities for testing with real data:

### Import from Local Dump File
```bash
# Import from a local SQL dump file
./dev db:import file:forge.dump
./dev db:import file:backup_20241201_143022.sql
```

### Import from External Database
```bash
# Import from production/staging database
./dev db:import external:prod.example.com:5432:prod_user:prod_pass:production:staging_test

# Import from local PostgreSQL instance
./dev db:import external:localhost:5432:postgres:password:source_db:test_db

# Import from another Docker container
./dev db:import external:host.docker.internal:5432:user:pass:source:target
```

### Format: `external:host:port:user:password:source_db:target_db`
- **host**: Database server hostname/IP
- **port**: Database port (usually 5432 for PostgreSQL)
- **user**: Database username
- **password**: Database password
- **source_db**: Source database name to import from
- **target_db**: Target database name in your dev environment

### Use Cases
- **Testing with Production Data**: Import production data to test new features
- **Multiple Test Environments**: Create separate databases for different testing scenarios
- **Data Migration Testing**: Test database migrations with real data
- **Performance Testing**: Use production-sized datasets for performance testing

### Safety Features
- Creates new database (doesn't overwrite existing data)
- Updates `.env` file to use the new database
- Preserves original database as backup
- Uses `--no-owner --no-privileges` for safe import

---

## 🆘 Troubleshooting Guide

### Common Issues & Solutions

**🚨 "502 Bad Gateway" on frontend?**
```bash
./dev logs              # Check all service logs (includes frontend)
./dev restart           # Restart all services
```

**🚨 Database connection errors?**
```bash
./dev health           # Check if database is running
./dev logs-db          # Check database logs
./dev restart          # Restart all services (including database)
```

**🚨 Search not working?**
```bash
./dev opensearch-health     # Check OpenSearch status
./dev opensearch-reindex    # Rebuild search indices
```

**🚨 Need to import production data for testing?**
```bash
# Import from local dump file
./dev db:import file:forge.dump

# Import from external database
./dev db:import external:prod.example.com:5432:user:pass:production:staging_test
```

**🚨 Permission errors?**
```bash
./dev shell
chmod -R 775 storage bootstrap/cache
```

**🚨 Everything is broken?**
```bash
./dev reset    # Nuclear option: destroy and rebuild everything
./dev setup    # Then run setup again
```

### Getting Help

1. **Check hosts configuration**: `./dev check-hosts`
2. **Check service health**: `./dev health`
3. **Check logs**: `./dev logs` or specific service logs
4. **Try restarting**: `./dev restart`
5. **Ask the team**: Post in the #engineering Slack channel with error logs
6. **Last resort**: `./dev reset` and `./dev setup`

### Setup Troubleshooting

**🚨 Repository cloning failed?**
```bash
# Check if you have SSH access to GitHub
ssh -T **************

# If SSH fails, you may need to:
# 1. Generate SSH key: ssh-keygen -t ed25519 -C "<EMAIL>"
# 2. Add to SSH agent: ssh-add ~/.ssh/id_ed25519
# 3. Add public key to GitHub: cat ~/.ssh/id_ed25519.pub
```

**🚨 Setup script permission denied?**
```bash
chmod +x setup.sh
chmod +x dev
```

**🚨 Git not found?**
```bash
# Install Git if not available
# macOS: brew install git
# Ubuntu/Debian: sudo apt-get install git
# Windows: Download from https://git-scm.com/
```

---

## 💡 Pro Tips & Advanced Usage

### Frontend Development

The frontend applications run on your host machine for optimal development experience:

```bash
# Start Clinic Portal (Shop)
cd ../clinic-portal-web-app
npm run start          # Runs at http://localhost:3000

# Start GPO Portal (in another terminal)
cd ../clinic-portal-web-app
npm run start:gpo      # Runs at http://localhost:3001

# Build for production
npm run build:all      # Builds both applications
```

### Running Custom Commands

```bash
# Run any Laravel Artisan command
./dev artisan list                    # List all available commands
./dev artisan make:controller MyController
./dev artisan migrate:status

# Run Composer commands
./dev composer-install --no-dev --optimize-autoloader
./dev composer-update --with-dependencies

# Access database directly
./dev shell-db
psql -U highfive -d highfive
```

### Performance Tips

- Use `./dev optimize` after pulling new code
- Use `./dev cache-clear` if you see stale data
- Monitor resource usage with `./dev logs` and `./dev health`
- Use `./dev precommit` before committing to catch issues early

---

## 📚 Additional Resources

- **[Laravel Documentation](https://laravel.com/docs)** - Backend framework
- **[React Documentation](https://react.dev/)** - Frontend framework  
- **[OpenSearch Documentation](https://opensearch.org/docs/)** - Search engine
- **[Docker Compose Documentation](https://docs.docker.com/compose/)** - Container orchestration
- **[Laravel Nova Documentation](https://nova.laravel.com/docs/)** - Admin panel

---

## 🎨 Built with ❤️ by the HighFive Team

Questions? Issues? Ideas? We're here to help!
- 📞 **Slack**: #engineering channel  
- 🐛 **Issues**: Create a GitHub issue
- 📖 **Docs**: This README and inline code comments

**Happy coding! 🚀** 