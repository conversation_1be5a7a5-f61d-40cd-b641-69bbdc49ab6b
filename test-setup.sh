#!/bin/bash
set -e

# HighFive Development Environment Test Script
# This script tests the complete setup process in a temporary environment

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Configuration
TEST_DIR="/tmp/highfive-test-$(date +%s)"
DEV_REPO="**************:highfive-vet/highfive-dev.git"
BACKEND_REPO="**************:highfive-vet/highfive-backend.git"
FRONTEND_REPO="**************:highfive-vet/clinic-portal-web-app.git"

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}======================================${NC}"
    echo -e "${BLUE}  HighFive Setup Test Script${NC}"
    echo -e "${BLUE}  Testing complete setup process${NC}"
    echo -e "${BLUE}======================================${NC}\n"
}

print_footer() {
    echo -e "\n${BLUE}======================================${NC}"
    echo -e "${BLUE}  Test Results Summary${NC}"
    echo -e "${BLUE}======================================${NC}\n"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up test environment..."
    if [ -d "$TEST_DIR" ]; then
        cd /tmp
        rm -rf "$TEST_DIR"
        print_success "Test directory cleaned up"
    fi
}

# Set trap to cleanup on exit
trap cleanup EXIT

# Check prerequisites
test_prerequisites() {
    print_status "Testing prerequisites..."
    
    local errors=0
    
    # Check Docker
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker is not installed"
        errors=$((errors + 1))
    elif ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running"
        errors=$((errors + 1))
    else
        print_success "Docker is available and running"
    fi
    
    # Check Git
    if ! command -v git >/dev/null 2>&1; then
        print_error "Git is not installed"
        errors=$((errors + 1))
    else
        print_success "Git is available"
    fi
    
    # Check Node.js
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is not installed"
        errors=$((errors + 1))
    else
        print_success "Node.js is available"
    fi
    
    # Check npm
    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is not installed"
        errors=$((errors + 1))
    else
        print_success "npm is available"
    fi
    
    if [ $errors -gt 0 ]; then
        print_error "Prerequisites check failed with $errors error(s)"
        return 1
    fi
    
    print_success "All prerequisites are satisfied!"
    return 0
}

# Test GitHub access
test_github_access() {
    print_status "Testing GitHub SSH access..."
    
    if ssh -T ************** 2>&1 | grep -q "successfully authenticated"; then
        print_success "GitHub SSH access confirmed"
        return 0
    else
        print_warning "GitHub SSH access not configured"
        return 1
    fi
}

# Test repository cloning
test_repository_cloning() {
    print_status "Testing repository cloning..."
    
    mkdir -p "$TEST_DIR"
    cd "$TEST_DIR"
    
    local errors=0
    
    # Test highfive-dev cloning
    print_status "Cloning highfive-dev..."
    if git clone "$DEV_REPO" highfive-dev 2>/dev/null; then
        print_success "✅ highfive-dev cloned successfully"
    else
        print_error "❌ Failed to clone highfive-dev"
        errors=$((errors + 1))
    fi
    
    # Test highfive-backend cloning
    print_status "Cloning highfive-backend..."
    if git clone "$BACKEND_REPO" highfive-backend 2>/dev/null; then
        print_success "✅ highfive-backend cloned successfully"
    else
        print_error "❌ Failed to clone highfive-backend"
        errors=$((errors + 1))
    fi
    
    # Test clinic-portal-web-app cloning
    print_status "Cloning clinic-portal-web-app..."
    if git clone "$FRONTEND_REPO" clinic-portal-web-app 2>/dev/null; then
        print_success "✅ clinic-portal-web-app cloned successfully"
    else
        print_error "❌ Failed to clone clinic-portal-web-app"
        errors=$((errors + 1))
    fi
    
    if [ $errors -gt 0 ]; then
        print_error "Repository cloning failed with $errors error(s)"
        return 1
    fi
    
    print_success "All repositories cloned successfully!"
    return 0
}

# Test Docker setup
test_docker_setup() {
    print_status "Testing Docker setup..."
    
    cd "$TEST_DIR/highfive-dev"
    
    # Check if dev script exists and is executable
    if [ ! -f "dev" ]; then
        print_error "dev script not found"
        return 1
    fi
    
    chmod +x dev
    
    # Test Docker build
    print_status "Building Docker containers..."
    if ./dev up --build 2>/dev/null; then
        print_success "✅ Docker containers built and started"
    else
        print_error "❌ Docker setup failed"
        return 1
    fi
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Test service health
    print_status "Testing service health..."
    if ./dev health >/dev/null 2>&1; then
        print_success "✅ Services are healthy"
    else
        print_warning "⚠️  Some services may not be healthy"
    fi
    
    return 0
}

# Test backend setup
test_backend_setup() {
    print_status "Testing backend setup..."
    
    cd "$TEST_DIR/highfive-dev"
    
    # Test if Laravel is accessible
    print_status "Testing Laravel application..."
    if docker-compose exec app php artisan --version 2>/dev/null; then
        print_success "✅ Laravel application is accessible"
    else
        print_warning "⚠️  Laravel application may not be fully set up (Nova dependency issue expected)"
    fi
    
    return 0
}

# Test frontend setup
test_frontend_setup() {
    print_status "Testing frontend setup..."
    
    cd "$TEST_DIR/clinic-portal-web-app"
    
    # Test npm install
    print_status "Installing frontend dependencies..."
    if npm install 2>/dev/null; then
        print_success "✅ Frontend dependencies installed"
    else
        print_error "❌ Frontend dependency installation failed"
        return 1
    fi
    
    # Test if node_modules exists
    if [ -d "node_modules" ]; then
        print_success "✅ Frontend setup completed"
    else
        print_error "❌ Frontend setup incomplete"
        return 1
    fi
    
    return 0
}

# Test database operations
test_database_operations() {
    print_status "Testing database operations..."
    
    cd "$TEST_DIR/highfive-dev"
    
    # Create test SQL file
    echo "CREATE TABLE test_setup (id SERIAL PRIMARY KEY, test_column VARCHAR(255)); INSERT INTO test_setup (test_column) VALUES ('setup_test');" > test_setup.sql
    
    # Test database import
    print_status "Testing database import..."
    if ./dev db:import file:test_setup.sql 2>/dev/null; then
        print_success "✅ Database import working"
    else
        print_error "❌ Database import failed"
        return 1
    fi
    
    # Test database export
    print_status "Testing database export..."
    if ./dev db:export 2>/dev/null; then
        print_success "✅ Database export working"
    else
        print_warning "⚠️  Database export may have issues"
    fi
    
    # Cleanup test file
    rm -f test_setup.sql
    
    return 0
}

# Test service accessibility
test_service_accessibility() {
    print_status "Testing service accessibility..."
    
    cd "$TEST_DIR/highfive-dev"
    
    local services=(
        "http://localhost:80"
        "http://localhost:5432"
        "http://localhost:6379"
        "http://localhost:8025"
    )
    
    local accessible=0
    local total=${#services[@]}
    
    for service in "${services[@]}"; do
        if curl -s "$service" >/dev/null 2>&1 || nc -z localhost $(echo "$service" | sed 's/.*://') 2>/dev/null; then
            print_success "✅ $service is accessible"
            accessible=$((accessible + 1))
        else
            print_warning "⚠️  $service may not be accessible"
        fi
    done
    
    if [ $accessible -eq $total ]; then
        print_success "All services are accessible!"
        return 0
    else
        print_warning "Some services may not be accessible ($accessible/$total)"
        return 0
    fi
}

# Main test execution
main() {
    print_header
    
    local test_results=()
    local total_tests=0
    local passed_tests=0
    
    print_status "Starting comprehensive setup test..."
    print_status "Test directory: $TEST_DIR"
    
    # Run all tests
    tests=(
        "test_prerequisites"
        "test_github_access"
        "test_repository_cloning"
        "test_docker_setup"
        "test_backend_setup"
        "test_frontend_setup"
        "test_database_operations"
        "test_service_accessibility"
    )
    
    for test in "${tests[@]}"; do
        total_tests=$((total_tests + 1))
        print_status "Running test: $test"
        
        if $test; then
            test_results+=("✅ $test: PASSED")
            passed_tests=$((passed_tests + 1))
        else
            test_results+=("❌ $test: FAILED")
        fi
        
        echo
    done
    
    # Print results
    print_footer
    
    echo -e "${BLUE}Test Results:${NC}"
    for result in "${test_results[@]}"; do
        echo "  $result"
    done
    
    echo
    echo -e "${BLUE}Summary:${NC}"
    echo -e "  Total tests: $total_tests"
    echo -e "  Passed: $passed_tests"
    echo -e "  Failed: $((total_tests - passed_tests))"
    
    if [ $passed_tests -eq $total_tests ]; then
        print_success "\n🎉 All tests passed! Setup script is working correctly."
        exit 0
    else
        print_error "\n⚠️  Some tests failed. Please review the setup process."
        exit 1
    fi
}

# Run main function
main "$@"
