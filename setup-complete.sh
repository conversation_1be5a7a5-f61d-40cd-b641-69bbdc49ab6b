#!/bin/bash
set -e

# HighFive Development Environment Complete Setup Script
# This script automates the entire setup process for new developers

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Configuration
WORKSPACE_DIR="${WORKSPACE_DIR:-highfive-workspace}"
DEV_REPO="**************:highfive-vet/highfive-dev.git"
BACKEND_REPO="**************:highfive-vet/highfive-backend.git"
FRONTEND_REPO="**************:highfive-vet/clinic-portal-web-app.git"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}======================================${NC}"
    echo -e "${BLUE}  HighFive Development Environment${NC}"
    echo -e "${BLUE}  Complete Setup Script${NC}"
    echo -e "${BLUE}======================================${NC}\n"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker is not installed. Please install Docker Desktop first."
        print_status "Download from: https://www.docker.com/products/docker-desktop/"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
    
    # Check Git
    if ! command -v git >/dev/null 2>&1; then
        print_error "Git is not installed. Please install Git first."
        print_status "Download from: https://git-scm.com/downloads"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is not installed. Please install Node.js first."
        print_status "Download from: https://nodejs.org/"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "All prerequisites are satisfied!"
}

# Check SSH access to GitHub
check_github_access() {
    print_status "Checking GitHub SSH access..."
    
    if ! ssh -T ************** 2>&1 | grep -q "successfully authenticated"; then
        print_warning "SSH access to GitHub not configured. You may need to:"
        print_status "1. Generate SSH key: ssh-keygen -t ed25519 -C '<EMAIL>'"
        print_status "2. Add to SSH agent: ssh-add ~/.ssh/id_ed25519"
        print_status "3. Add public key to GitHub: cat ~/.ssh/id_ed25519.pub"
        print_status "4. Test: ssh -T **************"
        
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_success "GitHub SSH access confirmed!"
    fi
}

# Setup hosts file entries
setup_hosts() {
    print_status "Setting up local hosts entries..."
    
    # Check if hosts entries already exist
    if grep -q "highfive.local" /etc/hosts; then
        print_success "Hosts entries already configured"
        return 0
    fi
    
    # Add hosts entries
    HOSTS_ENTRIES=(
        "127.0.0.1 highfive.local"
        "127.0.0.1 highfive-admin.local"
        "127.0.0.1 highfive-api.local"
        "127.0.0.1 highfive-mailpit.local"
        "127.0.0.1 highfive-opensearch.local"
        "127.0.0.1 highfive-opensearch-dashboards.local"
    )
    
    print_status "Adding hosts entries to /etc/hosts (requires sudo)..."
    
    for entry in "${HOSTS_ENTRIES[@]}"; do
        if ! grep -q "$entry" /etc/hosts; then
            echo "$entry" | sudo tee -a /etc/hosts > /dev/null
            print_success "Added: $entry"
        fi
    done
    
    print_success "Hosts configuration complete!"
}

# Clone repositories
clone_repositories() {
    print_status "Setting up repository structure..."
    
    # Create workspace directory
    if [ ! -d "$WORKSPACE_DIR" ]; then
        mkdir -p "$WORKSPACE_DIR"
        print_success "Created workspace directory: $WORKSPACE_DIR"
    fi
    
    cd "$WORKSPACE_DIR"
    
    # Clone highfive-dev if it doesn't exist
    if [ ! -d "highfive-dev" ]; then
        print_status "Cloning highfive-dev repository..."
        git clone "$DEV_REPO"
        print_success "✅ highfive-dev repository cloned"
    else
        print_success "✅ highfive-dev repository already exists"
    fi
    
    # Clone highfive-backend if it doesn't exist
    if [ ! -d "highfive-backend" ]; then
        print_status "Cloning highfive-backend repository..."
        git clone "$BACKEND_REPO"
        print_success "✅ highfive-backend repository cloned"
    else
        print_success "✅ highfive-backend repository already exists"
    fi
    
    # Clone clinic-portal-web-app if it doesn't exist
    if [ ! -d "clinic-portal-web-app" ]; then
        print_status "Cloning clinic-portal-web-app repository..."
        git clone "$FRONTEND_REPO"
        print_success "✅ clinic-portal-web-app repository cloned"
    else
        print_success "✅ clinic-portal-web-app repository already exists"
    fi
}

# Setup environment configuration
setup_environment() {
    print_status "Setting up environment configuration..."
    
    cd "highfive-dev"
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        if [ -f "env.example" ]; then
            print_status "Creating .env file from template..."
            cp env.example .env
            print_success "✅ Environment file created from template"
        else
            print_warning "No env.example found, creating basic .env file..."
            cat > .env << EOF
# HighFive Development Environment
APP_PORT=80
DB_PORT=5432
REDIS_PORT=6379
OPENSEARCH_PORT=9200
OPENSEARCH_DASHBOARDS_PORT=9600
MAILPIT_PORT=8025
EOF
            print_success "✅ Basic environment file created"
        fi
    else
        print_success "✅ Environment file already exists"
    fi
}

# Run the main setup
run_setup() {
    print_status "Running HighFive setup script..."
    
    # Make sure we're in the right directory
    cd "$WORKSPACE_DIR/highfive-dev"
    
    # Make scripts executable
    chmod +x dev
    chmod +x setup.sh
    
    # Run the setup
    print_status "Starting Docker services and installing dependencies..."
    ./dev setup
    
    print_success "✅ HighFive setup completed!"
}

# Setup frontend dependencies
setup_frontend() {
    print_status "Setting up frontend dependencies..."
    
    cd "$WORKSPACE_DIR/clinic-portal-web-app"
    
    # Install dependencies
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies (this may take a few minutes)..."
        npm install
        print_success "✅ Frontend dependencies installed"
    else
        print_success "✅ Frontend dependencies already installed"
    fi
}

# Verify setup
verify_setup() {
    print_status "Verifying setup..."
    
    cd "$WORKSPACE_DIR/highfive-dev"
    
    # Check if services are running
    if ./dev health > /dev/null 2>&1; then
        print_success "✅ Docker services are healthy"
    else
        print_warning "⚠️  Some Docker services may not be running"
    fi
    
    # Check if backend is accessible
    if curl -s http://highfive.local > /dev/null 2>&1; then
        print_success "✅ Backend is accessible at http://highfive.local"
    else
        print_warning "⚠️  Backend may not be accessible yet"
    fi
    
    # Check if frontend dependencies are installed
    if [ -d "../clinic-portal-web-app/node_modules" ]; then
        print_success "✅ Frontend dependencies are installed"
    else
        print_warning "⚠️  Frontend dependencies may not be installed"
    fi
}

# Display next steps
show_next_steps() {
    print_success "\n🎉 HighFive Development Environment Setup Complete!"
    
    echo -e "\n${BLUE}📋 Next Steps:${NC}"
    echo -e "1. ${GREEN}Start development:${NC}"
    echo -e "   cd $WORKSPACE_DIR/highfive-dev"
    echo -e "   ./dev up"
    echo -e ""
    echo -e "2. ${GREEN}Start frontend development:${NC}"
    echo -e "   cd $WORKSPACE_DIR/clinic-portal-web-app"
    echo -e "   npm run start          # Clinic Portal at http://localhost:3000"
    echo -e "   npm run start:gpo      # GPO Portal at http://localhost:3001"
    echo -e ""
    echo -e "3. ${GREEN}Access services:${NC}"
    echo -e "   - Backend: http://highfive.local"
    echo -e "   - Admin: http://highfive-admin.local"
    echo -e "   - API: http://highfive-api.local"
    echo -e "   - Mailpit: http://highfive-mailpit.local"
    echo -e "   - OpenSearch: http://highfive-opensearch.local"
    echo -e ""
    echo -e "4. ${GREEN}Useful commands:${NC}"
    echo -e "   ./dev help             # Show all available commands"
    echo -e "   ./dev health           # Check service health"
    echo -e "   ./dev logs             # View service logs"
    echo -e "   ./dev restart          # Restart all services"
    echo -e ""
    echo -e "5. ${GREEN}Database operations:${NC}"
    echo -e "   ./dev db:import file:your_dump.sql"
    echo -e "   ./dev db:export        # Export database"
    echo -e ""
    echo -e "${YELLOW}⚠️  Note:${NC} If you encounter Nova license issues, you'll need to:"
    echo -e "   - Configure your Nova license in ~/.composer/auth.json"
    echo -e "   - Or contact your team lead for Nova access"
}

# Main execution
main() {
    print_header
    
    # Check if running from the right location
    if [ ! -f "setup-complete.sh" ]; then
        print_error "Please run this script from the highfive-dev directory"
        exit 1
    fi
    
    # Get workspace directory from user
    read -p "Enter workspace directory name (default: highfive-workspace): " -r
    if [ ! -z "$REPLY" ]; then
        WORKSPACE_DIR="$REPLY"
    fi
    
    print_status "Using workspace directory: $WORKSPACE_DIR"
    
    # Run all setup steps
    check_prerequisites
    check_github_access
    setup_hosts
    clone_repositories
    setup_environment
    run_setup
    setup_frontend
    verify_setup
    show_next_steps
    
    print_success "\n🚀 HighFive is ready for development!"
}

# Run main function
main "$@"
