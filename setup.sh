#!/bin/bash
set -e

# Change to the script's directory
cd "$(dirname "$0")"

GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    if ! command -v docker >/dev/null 2>&1; then
        echo "Error: Docker is not installed"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        echo "Error: Docker is not running"
        exit 1
    fi
    
    if ! command -v git >/dev/null 2>&1; then
        echo "Error: Git is not installed"
        exit 1
    fi
}

# Check and clone required repositories
check_and_clone_repositories() {
    print_status "Checking required repositories..."
    
    # Check if we're in the right directory structure
    if [ ! -f "docker-compose.yml" ]; then
        print_warning "Error: This script must be run from the highfive-dev directory"
        exit 1
    fi
    
    # Check backend repository
    if [ ! -d "../highfive-backend" ]; then
        print_warning "Backend repository not found. Cloning..."
        cd ..
        <NAME_EMAIL>:highfive-vet/highfive-backend.git
        cd highfive-dev
        print_success "✅ Backend repository cloned"
    else
        print_success "✅ Backend repository exists"
    fi
    
    # Check frontend repository
    if [ ! -d "../clinic-portal-web-app" ]; then
        print_warning "Frontend repository not found. Cloning..."
        cd ..
        <NAME_EMAIL>:highfive-vet/clinic-portal-web-app.git
        cd highfive-dev
        print_success "✅ Frontend repository cloned"
    else
        print_success "✅ Frontend repository exists"
    fi
    
    # Verify both repositories exist
    if [ ! -d "../highfive-backend" ] || [ ! -d "../clinic-portal-web-app" ]; then
        print_warning "Error: Failed to clone required repositories"
        exit 1
    fi
    
    print_success "All required repositories are available"
}

# Start services
start_services() {
    print_status "Stopping existing services..."
    docker-compose down
    
    # Copy environment file if it doesn't exist
    if [ ! -f ".env" ] && [ -f "env.example" ]; then
        print_status "Creating .env file from template..."
        cp env.example .env
    fi
    
    print_status "Starting Docker services..."
    docker-compose up -d --build
}

# Wait for services
wait_for_services() {
    print_status "Waiting for services..."
    
    # Wait for database
    timeout=30
    while ! docker-compose exec -T database pg_isready -U highfive >/dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        [ $timeout -le 0 ] && break
    done
    
    # Wait for app
    timeout=30
    while ! curl -sf http://admin.highfive.local/up >/dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        [ $timeout -le 0 ] && break
    done
}

# Install dependencies
install_dependencies() {
    print_status "Installing PHP dependencies..."
    
    # Install dependencies (auth.json is now mounted as volume)
    docker-compose exec -T app composer install --optimize-autoloader --no-interaction
    
    # Set permissions only for directories that need it (avoid .git and vendor)
    # docker-compose exec -T app chmod -R 755 storage bootstrap/cache 2>/dev/null || true
}

# Setup Laravel
setup_laravel() {
    print_status "Setting up Laravel..."
    
    # Generate key if needed
    if ! grep -q "APP_KEY=base64:" ".env"; then
        docker-compose exec -T app php artisan key:generate --force
    fi
    
    # Run migrations
    docker-compose exec -T app php artisan migrate --force
    
    # Basic seeding
    docker-compose exec -T app php artisan db:seed --force 2>/dev/null || true

    # Install demo data
    docker-compose exec -T app php artisan demo:install --force 2>/dev/null || true
}

# Setup frontend dependencies
setup_frontend() {
    print_status "Setting up frontend dependencies..."
    
    if [ -d "../clinic-portal-web-app" ]; then
        cd ../clinic-portal-web-app
        
        if [ ! -d "node_modules" ]; then
            print_status "Installing frontend dependencies..."
            npm install
            print_success "✅ Frontend dependencies installed"
        else
            print_success "✅ Frontend dependencies already installed"
        fi
        
        cd ../highfive-dev
    else
        print_warning "Frontend repository not found, skipping frontend setup"
    fi
}

# Main function
main() {
    echo "🚀 HighFive Setup"
    
    check_prerequisites
    check_and_clone_repositories
    start_services
    wait_for_services
    install_dependencies
    setup_laravel
    setup_frontend
    
    print_success "Setup complete!"
    echo ""
    echo "==================== Service URLs and Credentials ===================="
    echo "🔑 Laravel Nova Admin:    http://admin.highfive.local/nova"
    echo "    • Login: <EMAIL> / password"
    echo "📊 Laravel Horizon:       http://admin.highfive.local/horizon"
    echo "🔍 OpenSearch API:        http://opensearch:9200"
    echo "📈 OpenSearch Dashboards: http://localhost:5601"
    echo "💾 PostgreSQL:            database:5432 (DB: highfive, User: highfive, Pass: secret)"
    echo "⚡ Redis:                 redis:6379"
    echo "📧 Mailpit:               http://mailpit:8025"
    echo ""
    echo "==================== Frontend Development ===================="
    echo "🌐 Clinic Portal:         http://localhost:3000 (run: npm run start)"
    echo "🏢 GPO Portal:            http://localhost:3001 (run: npm run start:gpo)"
    echo ""
    echo "==================== Next Steps ===================="
    echo "🚀 Start frontend development:"
    echo "   cd ../clinic-portal-web-app"
    echo "   npm run start          # Clinic Portal at http://localhost:3000"
    echo "   npm run start:gpo      # GPO Portal at http://localhost:3001"
    echo "======================================================================="
    echo ""
}

main "$@" 