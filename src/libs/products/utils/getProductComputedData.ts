export const getProductOfferComputedData = (productOffer: {
  rebatePercent: string | null;
  isRecommended: boolean;
  clinicPrice: string | null;
  price: string | null;
}) => {
  const { rebatePercent, isRecommended, clinicPrice, price } = productOffer;

  const priceNumber = price ? +price : 0;
  const clinicPriceNumber = clinicPrice ? +clinicPrice : 0;
  const baseSalePrice = clinicPrice ? +clinicPrice : priceNumber;
  const hasRebate = rebatePercent && +rebatePercent > 0;
  const salePrice =
    isRecommended && hasRebate
      ? baseSalePrice - baseSalePrice * (+rebatePercent / 100)
      : baseSalePrice;
  const originalPrice =
    priceNumber > clinicPriceNumber ? priceNumber : clinicPriceNumber;
  const hasVendorSavings =
    originalPrice && salePrice && !isRecommended && originalPrice > salePrice;
  const hasGpoSavings =
    originalPrice && salePrice && isRecommended && originalPrice > salePrice;
  const vendorSavings = hasVendorSavings ? originalPrice - salePrice : 0;
  const gpoSavings = hasGpoSavings ? originalPrice - salePrice : 0;

  return {
    hasRebate,
    salePrice,
    originalPrice,
    hasVendorSavings,
    hasGpoSavings,
    vendorSavings,
    gpoSavings,
  };
};
