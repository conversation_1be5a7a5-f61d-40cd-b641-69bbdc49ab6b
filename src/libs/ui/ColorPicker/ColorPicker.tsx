import { forwardRef } from 'react';
import { Select } from '@/libs/form/Select';

export const PREDEFINED_COLORS = [
  { name: 'Red', value: '#EF4444', bgClass: 'bg-red-500' },
  { name: 'Blue', value: '#3B82F6', bgClass: 'bg-blue-500' },
  { name: 'Green', value: '#10B981', bgClass: 'bg-green-500' },
  { name: 'Yellow', value: '#F59E0B', bgClass: 'bg-yellow-500' },
  { name: 'Purple', value: '#8B5CF6', bgClass: 'bg-purple-500' },
  { name: 'Pink', value: '#EC4899', bgClass: 'bg-pink-500' },
  { name: 'Orange', value: '#F97316', bgClass: 'bg-orange-500' },
  { name: 'Gray', value: '#6B7280', bgClass: 'bg-gray-500' },
];

export interface ColorPickerProps {
  value?: string | null;
  onChange?: (color: string | null) => void;
  label?: string;
  className?: string;
  allowNone?: boolean;
  error?: string;
  style?: React.CSSProperties;
}

export const ColorPicker = forwardRef<HTMLSelectElement, ColorPickerProps>(
  (
    {
      value,
      onChange,
      label,
      className = '',
      allowNone = false,
      error,
      style,
      ...rest
    },
    ref,
  ) => {
    const handleColorChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
      const colorValue = event.target.value;
      const newValue =
        colorValue === '' || colorValue === 'none' ? null : colorValue;
      onChange?.(newValue);
    };

    const currentColor = value
      ? PREDEFINED_COLORS.find((color) => color.value === value)
      : null;

    const options = PREDEFINED_COLORS.map((color) => ({
      value: color.value,
      label: color.name,
    }));

    if (allowNone) {
      options.unshift({ value: 'none', label: 'Select a color' });
    }

    return (
      <div className={`flex flex-col ${className}`}>
        <div className="relative">
          <Select
            ref={ref}
            label={label}
            value={value === null ? (allowNone ? 'none' : '') : value}
            onChange={handleColorChange}
            error={error}
            options={options}
            showEmptyOption={!allowNone}
            style={{
              paddingLeft: currentColor ? '2.5rem' : undefined,
              ...style,
            }}
            {...rest}
          />
          {currentColor && (
            <div className="pointer-events-none absolute top-auto bottom-0 left-3 flex h-10 items-center">
              <div
                className={`h-4 w-4 rounded-full ${currentColor.bgClass}`}
                style={{ backgroundColor: currentColor.value }}
              />
            </div>
          )}
        </div>
      </div>
    );
  },
);

ColorPicker.displayName = 'ColorPicker';
