import { RouterProvider } from 'react-router-dom';
import { Notifications } from '@mantine/notifications';
import { ThemeProvider } from '@/providers/ThemeProvider';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from '@/libs/query/queryClient';

import { router } from './routes';
import EnvironmentBanner from '@/components/EnvironmentBanner';

import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import '@/assets/css/index.css';
import '@/assets/css/tailwind.css';

export const GpoPortal = () => {
  return (
    <ThemeProvider>
      <QueryClientProvider client={queryClient}>
        <EnvironmentBanner />
        <RouterProvider router={router} />
        <Notifications position="top-right" zIndex={1000} />
      </QueryClientProvider>
    </ThemeProvider>
  );
};
