import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { GpoVendorData } from '../../types';
import { Image } from '@mantine/core';
import { Modal } from '@/components';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import styles from './VendorSettingsModal.module.css';
import { MODAL_NAME } from '@/constants';
import { Input } from '@/libs/form/Input';
import { useForm } from 'react-hook-form';
import * as Yup from 'yup';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button } from '@/libs/ui/Button/Button';
import { useQueryClient } from '@tanstack/react-query';
import { useAccountSettings } from '../../hooks/useAccountSettings';
import { useEffect } from 'react';
import { moneyMask } from '@/libs/form/masks';
import { fetchApi } from '@/libs/utils/api';

type VendorSettingsModalOptions = ModalOptionProps & {
  vendor: GpoVendorData;
};

type VendorSettingsForm = {
  goalValue: string;
};

const SCHEMA = Yup.object().shape({
  goalValue: Yup.string()
    .required('Goal value is required')
    .test('is-valid-amount', 'Goal value must be greater than 0', (value) => {
      if (!value) return false;
      const numericValue = value.replace(/[$,]/g, '');
      const parsed = parseFloat(numericValue);
      return !isNaN(parsed) && parsed > 0;
    }),
});

export const VendorSettingsModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const { vendor } = modalOption as VendorSettingsModalOptions;
  const queryClient = useQueryClient();
  const { accountSettings } = useAccountSettings();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<VendorSettingsForm>({
    resolver: yupResolver(SCHEMA),
    defaultValues: {
      goalValue: '',
    },
  });

  useEffect(() => {
    if (vendor && accountSettings.length > 0) {
      const existingVendorGoalsSetting = accountSettings.find(
        (setting) => setting.setting_key === 'vendor_goals',
      );

      const existingVendorGoal = existingVendorGoalsSetting?.value.goals.find(
        (goal) => goal.vendor_id === vendor.id,
      );

      if (existingVendorGoal) {
        reset({
          goalValue: `$${existingVendorGoal.goal_amount.toLocaleString(
            'en-US',
            {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            },
          )}`,
        });
      }
    }
  }, [vendor, accountSettings, reset]);

  const { apiRequest: handleSettings, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      if (!vendor) return;

      // Find existing vendor goals setting
      const existingVendorGoalsSetting = accountSettings.find(
        (setting) => setting.setting_key === 'vendor_goals',
      );

      // Determine if we're creating new vendor goals or updating existing ones
      const isCreatingNewVendorGoals = !existingVendorGoalsSetting;
      const numericGoalValue = parseFloat(
        values.goalValue.replace(/[$,]/g, ''),
      );

      let goals = [];

      if (isCreatingNewVendorGoals) {
        // Create new vendor goals setting with this vendor
        goals = [
          {
            enabled: true,
            vendor_id: vendor.id,
            goal_amount: numericGoalValue,
          },
        ];
      } else {
        // Update existing vendor goals setting
        const existingGoals = existingVendorGoalsSetting.value.goals.filter(
          (goal) => goal.vendor_id !== vendor.id,
        );

        goals = [
          ...existingGoals,
          {
            enabled: true,
            vendor_id: vendor.id,
            goal_amount: numericGoalValue,
          },
        ];
      }

      const payload = {
        setting_key: 'vendor_goals',
        value: {
          goals,
        },
      };

      const url = isCreatingNewVendorGoals
        ? '/gpo/account-settings'
        : `/gpo/account-settings/${existingVendorGoalsSetting.id}`;

      const method = isCreatingNewVendorGoals ? 'POST' : 'PATCH';

      await fetchApi(url, {
        method,
        body: payload,
        authStrategy: 'token',
      });

      queryClient.invalidateQueries({ queryKey: ['account-settings'] });
      queryClient.invalidateQueries({ queryKey: ['vendors-overview'] });

      reset();
      closeModal();
    }),
  });

  // Set default value when vendor changes
  useEffect(() => {
    if (vendor) {
      const existingVendorGoalsSetting = accountSettings.find(
        (setting) => setting.setting_key === 'vendor_goals',
      );

      const existingVendorGoal = existingVendorGoalsSetting?.value.goals.find(
        (goal) => goal.vendor_id === vendor.id,
      );

      reset({
        goalValue: existingVendorGoal?.goal_amount
          ? `$${existingVendorGoal.goal_amount.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}`
          : '',
      });
    }
  }, [vendor, accountSettings, reset]);

  if (!vendor) {
    return null;
  }

  return (
    <Modal
      name={MODAL_NAME.VENDOR_SETTINGS}
      size="md"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
    >
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <Image
            src={vendor.imageUrl}
            alt={vendor.name}
            fallbackSrc={defaultProductImgUrl}
            className={styles.vendorLogo}
            w="130px"
          />
          <form onSubmit={handleSettings} className="w-full">
            <div className="mb-4">
              <Input
                label="Vendor Goal Amount"
                mask={moneyMask}
                {...register('goalValue')}
                error={errors.goalValue?.message}
                placeholder="Enter vendor goal amount"
              />
            </div>
            <Button loading={isLoading}>Save</Button>
          </form>
        </div>
      </div>
    </Modal>
  );
};
