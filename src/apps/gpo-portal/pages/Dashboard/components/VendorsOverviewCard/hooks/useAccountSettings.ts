import { useQuery } from '@tanstack/react-query';
import { fetchApi } from '@/libs/utils/api';

export interface VendorGoal {
  enabled: boolean;
  vendor_id: string;
  goal_amount: number;
}

export interface AccountSettings {
  id: string;
  gpo_account_id: string;
  setting_key: 'performance_threshold' | 'vendor_goals';
  value: {
    goals: VendorGoal[];
  };
  created_at: string;
  updated_at: string;
}

export const useAccountSettings = () => {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['account-settings'],
    queryFn: async () => {
      const response = await fetchApi<AccountSettings[]>(
        '/gpo/account-settings',
        {
          authStrategy: 'token',
        },
      );

      return response;
    },
  });

  return {
    accountSettings: data || [],
    isLoading,
    error,
    refetch,
  };
};
