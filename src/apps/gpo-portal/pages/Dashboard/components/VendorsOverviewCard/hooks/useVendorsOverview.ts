import { useQuery } from '@tanstack/react-query';
import { fetchApi } from '@/libs/utils/api';
import { VendorsOverviewResponse, VendorsOverviewFilters } from '../types';
import dayjs from 'dayjs';
import { DEFAULT_SERVER_DATE_FORMAT } from '@/constants';

export const useVendorsOverview = (
  filters: VendorsOverviewFilters = {
    date_from: dayjs().startOf('year'),
    date_to: dayjs().endOf('year'),
    vendor_type: undefined,
    status: 'all',
    min_spend: undefined,
    max_spend: undefined,
  },
) => {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['vendors-overview', filters],
    queryFn: async () => {
      const response = await fetchApi<VendorsOverviewResponse>(
        `/gpo/vendors-overview?date_from=${filters.date_from?.format(
          DEFAULT_SERVER_DATE_FORMAT,
        )}&date_to=${filters.date_to?.format(DEFAULT_SERVER_DATE_FORMAT)}`,
        {
          method: 'GET',
          authStrategy: 'token',
        },
      );

      return response;
    },
    enabled: !!(filters.date_from && filters.date_to),
  });

  return {
    data: data || { total_spend: 0, vendors: [] },
    isLoading,
    error: error?.message || null,
    filters,
    updateFilters: () => {
      // This will be handled by the parent component
      // by passing new filters to this hook
    },
    refresh: refetch,
  };
};
