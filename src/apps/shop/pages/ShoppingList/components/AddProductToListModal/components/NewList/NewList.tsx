import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import { Input } from '@/libs/form/Input';
import { ColorPicker } from '@/libs/ui/ColorPicker/ColorPicker';
import { Button } from '@/libs/ui/Button/Button';
import { ProductDisplayCard } from '../../../ProductDisplayCard/ProductDisplayCard';
import { ProductType } from '@/types';

type Props = {
  product: ProductType;
  selectedOfferId?: string;
  onSubmit: (data: FormValues) => void;
  loading?: boolean;
};

type FormValues = {
  listName: string;
  label?: string;
  selectedColor?: string | null;
};

const SCHEMA = Yup.object().shape({
  listName: Yup.string().required('List name is required'),
  label: Yup.string().optional(),
  selectedColor: Yup.string()
    .nullable()
    .optional()
    .when('label', {
      is: (label: string) => label && label.trim().length > 0,
      then: (schema) =>
        schema.required('Color is required when label is provided'),
      otherwise: (schema) => schema.nullable().optional(),
    }),
});

export const NewList = ({
  product,
  selectedOfferId,
  onSubmit: onSubmitProp,
  loading = false,
}: Props) => {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA),
    defaultValues: {
      listName: '',
      label: '',
      selectedColor: null,
    },
    mode: 'onChange',
  });

  const watchedColor = watch('selectedColor');

  const onSubmit = (data: FormValues) => {
    onSubmitProp(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4" role="form">
      <div className="flex flex-col gap-5 rounded-md border border-black/[0.06] bg-white p-4">
        <Input
          label="List name"
          {...register('listName')}
          placeholder="Enter list name"
          error={errors.listName?.message}
        />
        <ProductDisplayCard
          product={product}
          selectedOfferId={selectedOfferId}
        />

        <div className="grid grid-cols-2 gap-4">
          <ColorPicker
            label="Add color"
            value={watchedColor}
            onChange={(color) => setValue('selectedColor', color)}
            allowNone={true}
            error={errors.selectedColor?.message}
          />

          <div>
            <Input
              label="Add label"
              {...register('label')}
              placeholder="Enter label"
              error={errors.label?.message}
            />
          </div>
        </div>
      </div>

      <Button
        type="submit"
        loading={loading}
        disabled={!watch('listName')?.trim()}
        className="w-full"
        variant="secondary"
      >
        Add to List
      </Button>
    </form>
  );
};
