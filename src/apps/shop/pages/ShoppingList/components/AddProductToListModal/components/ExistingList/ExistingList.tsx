import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import { Select } from '@/libs/form/Select';
import { Input } from '@/libs/form/Input';
import { ColorPicker } from '@/libs/ui/ColorPicker/ColorPicker';
import { Button } from '@/libs/ui/Button/Button';
import { ProductDisplayCard } from '../../../ProductDisplayCard/ProductDisplayCard';
import { ProductType } from '@/types';

type Props = {
  product: ProductType;
  selectedOfferId?: string;
  onSubmit: (data: FormValues) => void;
  loading?: boolean;
};

type FormValues = {
  selectedListId: string;
  label?: string;
  selectedColor?: string | null;
};

const SCHEMA = Yup.object().shape({
  selectedListId: Yup.string().required('Please select a list'),
  label: Yup.string().optional(),
  selectedColor: Yup.string()
    .nullable()
    .optional()
    .when('label', {
      is: (label: string) => label && label.trim().length > 0,
      then: (schema) =>
        schema.required('Color is required when label is provided'),
      otherwise: (schema) => schema.nullable().optional(),
    }),
});

export const ExistingList = ({
  product,
  selectedOfferId,
  onSubmit: onSubmitProp,
  loading = false,
}: Props) => {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA),
    defaultValues: {
      selectedListId: '',
      label: '',
      selectedColor: null,
    },
    mode: 'onChange',
  });

  const watchedColor = watch('selectedColor');

  const existingLists = [
    { id: '1', name: 'My Shopping List 1', color: '#EF4444' },
    { id: '2', name: 'Emergency Supplies', color: '#10B981' },
    { id: '3', name: 'Monthly Orders', color: '#3B82F6' },
    { id: '4', name: 'Special Items', color: '#8B5CF6' },
  ];

  const onSubmit = (data: FormValues) => {
    onSubmitProp(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4" role="form">
      <div className="flex flex-col gap-5 rounded-md border border-black/[0.06] bg-white p-4">
        <ProductDisplayCard
          product={product}
          selectedOfferId={selectedOfferId}
        />
        <Select
          label="Select a list"
          {...register('selectedListId')}
          error={errors.selectedListId?.message}
          showEmptyOption={true}
          options={existingLists.map((list) => ({
            value: list.id,
            label: list.name,
          }))}
        />
        <div className="grid grid-cols-2 gap-4">
          <ColorPicker
            label="Add color"
            value={watchedColor}
            onChange={(color) => setValue('selectedColor', color)}
            allowNone={true}
            error={errors.selectedColor?.message}
          />

          <Input
            label="Add label"
            {...register('label')}
            placeholder="Enter label"
            error={errors.label?.message}
          />
        </div>
      </div>

      <Button
        type="submit"
        loading={loading}
        disabled={!watch('selectedListId')}
        className="w-full"
        variant="secondary"
      >
        Add to List
      </Button>
    </form>
  );
};
