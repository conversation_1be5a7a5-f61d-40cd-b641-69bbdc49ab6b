import { useState } from 'react';
import { Tabs } from '@/libs/ui/Tabs/Tabs';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import { ProductType } from '@/types';
import styles from './AddProductToListModal.module.css';
import { NewList } from './components/NewList/NewList';
import { ExistingList } from './components/ExistingList/ExistingList';

type AddProductToListModalOptions = ModalOptionProps & {
  product: ProductType;
  selectedOfferId?: string;
};

export const AddProductToListModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const { product, selectedOfferId } =
    modalOption as AddProductToListModalOptions;

  const [activeTab, setActiveTab] = useState(1); // 0 = existing, 1 = new
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateList = async (formData: {
    listName: string;
    label?: string;
    selectedColor?: string | null;
  }) => {
    setIsLoading(true);

    try {
      console.log('Creating list:', {
        mode: 'new',
        listName: formData.listName,
        color: formData.selectedColor,
        label: formData.label,
        product,
        selectedOfferId,
      });

      await new Promise((resolve) => setTimeout(resolve, 1000));

      closeModal();
    } catch (error) {
      console.error('Failed to create list:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToExistingList = async (formData: {
    selectedListId: string;
    label?: string;
    selectedColor?: string | null;
  }) => {
    setIsLoading(true);

    try {
      console.log('Adding to existing list:', {
        mode: 'existing',
        selectedListId: formData.selectedListId,
        color: formData.selectedColor,
        label: formData.label,
        product,
        selectedOfferId,
      });

      await new Promise((resolve) => setTimeout(resolve, 1000));

      closeModal();
    } catch (error) {
      console.error('Failed to add to list:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const tabs = [
    {
      label: 'Add to Existing List',
      onClick: (index: number) => setActiveTab(index),
    },
    {
      label: 'Create a New List',
      onClick: (index: number) => setActiveTab(index),
    },
  ];

  if (!product) {
    return null;
  }

  return (
    <Modal
      name={MODAL_NAME.ADD_PRODUCT_TO_LIST}
      withCloseButton
      size="520px"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
    >
      <div className="flex max-w-md flex-col items-center">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Add Product to List
          </h2>
        </div>

        <div className="space-y-6">
          <div>
            <Tabs active={activeTab} tabs={tabs} />
          </div>

          <div className="mt-4">
            {activeTab === 0 && (
              <ExistingList
                product={product}
                selectedOfferId={selectedOfferId}
                onSubmit={handleAddToExistingList}
                loading={isLoading}
              />
            )}
            {activeTab === 1 && (
              <NewList
                product={product}
                selectedOfferId={selectedOfferId}
                onSubmit={handleCreateList}
                loading={isLoading}
              />
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};
