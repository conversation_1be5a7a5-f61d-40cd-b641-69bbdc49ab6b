import { ProductType } from '@/types';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { LastOrderedOn } from '@/libs/ui/LastOrderedOn/LastOrderedOn';

export interface ProductDisplayCardProps {
  product: ProductType;
  selectedOfferId?: string;
  className?: string;
}

export const ProductDisplayCard = ({
  product,
  selectedOfferId,
  className = '',
}: ProductDisplayCardProps) => {
  const selectedOffer = selectedOfferId
    ? product.offers.find((offer) => offer.id === selectedOfferId)
    : product.offers[0];

  if (!selectedOffer) {
    return null;
  }

  const { name, imageUrl } = product;
  const { vendorSku, vendor, stockStatus, lastOrderedAt } = selectedOffer;

  return (
    <div className={`rounded-lg border border-blue-700 p-4 ${className}`}>
      <div className="flex items-center gap-4">
        <div className="flex h-16 w-16 flex-shrink-0 items-center justify-center rounded border border-gray-200 bg-white">
          <img
            src={imageUrl || defaultProductImgUrl}
            alt={name}
            className="h-12 w-12 object-scale-down"
          />
        </div>

        <div className="min-w-0 flex-1">
          <div className="mb-1 flex items-center gap-2">
            <span className="text-xs font-semibold text-green-700 uppercase">
              {stockStatus === 'IN_STOCK'
                ? 'IN STOCK'
                : stockStatus.replaceAll('_', ' ')}
            </span>
          </div>

          <h3 className="mb-1 line-clamp-2 text-base font-semibold text-gray-900">
            {name}
          </h3>

          <div className="flex items-center gap-4 text-xs text-gray-600">
            <span className="font-medium text-black/60">
              SKU:
              <span className="font-medium text-black">{vendorSku}</span>
            </span>
            <span className="font-semibold">{vendor.name}</span>
            {lastOrderedAt && (
              <LastOrderedOn
                lastOrderedAt={lastOrderedAt}
                lastOrderedQuantity={10}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
