import { fetchApi } from '@/libs/utils/api';
import { FetchPreviouslyPurchasedParams } from '../../types';

const parseQueryParams = (params: FetchPreviouslyPurchasedParams) => {
  const { search, perPage, page } = params;

  let queryString = '';

  if (search) {
    queryString += `filter[search]=${encodeURIComponent(search)}`;
  }

  if (perPage) {
    const pagePrefix = queryString ? '&' : '';
    queryString += `${pagePrefix}page[size]=${perPage}`;
  }

  if (page) {
    const pagePrefix = queryString ? '&' : '';
    queryString += `${pagePrefix}page[number]=${page}`;
  }

  return queryString;
};

export const fetchPreviouslyPurchased = async ({
  params,
  beforeStart = () => {},
  onSuccess = () => {},
  onError = () => {},
  afterDone = () => {},
}: FetchPreviouslyPurchasedProps) => {
  beforeStart();

  const queryString = parseQueryParams(params);
  try {
    const response = await fetchApi<PreviouslyPurchasedResponse>(
      `/previously-purchased?${queryString}`,
    );

    onSuccess({
      items: response.data,
      total: response.meta.total,
    });
  } catch {
    onError();
  }

  afterDone();
};
