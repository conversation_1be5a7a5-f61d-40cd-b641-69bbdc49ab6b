import { useState, useEffect } from 'react';
import { HeaderSearch } from '@/libs/ui/HeaderSearch/HeaderSearch';
import { Pagination } from '@/libs/ui/Pagination/Pagination';
import { Loader } from '@/libs/ui/Loader/Loader';
import dayjs from 'dayjs';
import { usePreviouslyPurchased } from './hooks/usePreviouslyPurchased';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import { PreviouslyPurchasedItem } from './components/PreviouslyPurchasedItem/PreviouslyPurchasedItem';

export const PreviouslyPurchased = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState('12');

  const { items, total, isLoading, fetchItems } = usePreviouslyPurchased({
    perPage: parseInt(itemsPerPage),
    page: 1,
  });

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setPage(1);
  };

  const lastOrderedAt = items.length > 0 ? items[0].lastOrderedAt : null;

  useEffect(() => {
    fetchItems({
      search: searchQuery || undefined,
      perPage: parseInt(itemsPerPage),
      page,
    });
  }, [searchQuery, page, itemsPerPage]);

  return (
    <div className="w-full">
      <div className="mb-6 flex items-center justify-between rounded-lg border border-black/[0.06] bg-white p-5">
        <div className="flex flex-col gap-1">
          <h2 className="text-sm font-semibold">
            Your Previously Purchased Items
          </h2>
          <div className="flex items-center gap-4 text-xs text-black/50">
            <span>
              Total of Items:{' '}
              <span className="font-semibold text-black/70">{total}</span>
            </span>
            <div className="divider-v"></div>
            <span>
              Last Update:{' '}
              <span className="font-semibold text-black/70">
                {!!lastOrderedAt &&
                  dayjs(lastOrderedAt).format(DEFAULT_DISPLAY_DATE_FORMAT)}
              </span>
            </span>
          </div>
        </div>

        <HeaderSearch
          placeholder="Search item"
          value={searchQuery}
          onChange={handleSearch}
          onEnter={handleSearch}
          rootClassName="w-80"
        />
      </div>

      <div className="mb-6 flex flex-col gap-1 rounded-lg bg-[#F8FBFD] p-4">
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader size="md" />
          </div>
        )}

        {!isLoading && items.length > 0 && (
          <div className="flex flex-col gap-4">
            {items.map((item) => (
              <PreviouslyPurchasedItem key={item.product.id} item={item} />
            ))}
          </div>
        )}
      </div>

      <Pagination
        page={page}
        onPageChange={(newPage: number) => {
          setPage(newPage);
        }}
        onChangeItemsPerPage={(newItemsPerPage: string) => {
          setItemsPerPage(newItemsPerPage);
          setPage(1);
        }}
        itemsPerPage={itemsPerPage}
        limitOptions={[
          { value: '12', label: '12' },
          { value: '24', label: '24' },
          { value: '48', label: '48' },
          { value: '60', label: '60' },
        ]}
        total={total}
      />
    </div>
  );
};
