import { useCallback, useEffect, useState } from 'react';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { fetchPreviouslyPurchased } from '../services/fetchPreviouslyPurchased';
import {
  FetchPreviouslyPurchasedParams,
  PreviouslyPurchasedItemType,
} from '../../types';

type UsePreviouslyPurchasedState = {
  items: PreviouslyPurchasedItemType[];
  total: number;
  isLoading: boolean;
  errorOnLoading: boolean;
};

type UsePreviouslyPurchasedReturn = UsePreviouslyPurchasedState & {
  fetchItems: (params: FetchPreviouslyPurchasedParams) => void;
  refetch: () => void;
};

export const usePreviouslyPurchased = (
  initialParams: FetchPreviouslyPurchasedParams = {},
): UsePreviouslyPurchasedReturn => {
  const [state, setState] = useState<UsePreviouslyPurchasedState>({
    items: [],
    total: 0,
    isLoading: false,
    errorOnLoading: false,
  });

  const [currentParams, setCurrentParams] =
    useState<FetchPreviouslyPurchasedParams>(initialParams);

  // Debounce search queries to avoid too many API calls
  const debouncedSearchQuery = useDebounce(currentParams.search || '', 500);

  const fetchItemsInternal = useCallback(
    (params: FetchPreviouslyPurchasedParams) => {
      fetchPreviouslyPurchased({
        params,
        beforeStart: () => {
          setState((prev) => ({
            ...prev,
            isLoading: true,
            errorOnLoading: false,
          }));
        },
        onSuccess: (data) => {
          setState((prev) => ({
            ...prev,
            items: data.items,
            total: data.total,
          }));
        },
        onError: () => {
          setState((prev) => ({ ...prev, errorOnLoading: true }));
        },
        afterDone: () => {
          setState((prev) => ({ ...prev, isLoading: false }));
        },
      });
    },
    [],
  );

  const fetchItems = useCallback((params: FetchPreviouslyPurchasedParams) => {
    setCurrentParams(params);
  }, []);

  // Effect to trigger API call when debounced search query or other params change
  useEffect(() => {
    const paramsWithDebouncedSearch = {
      ...currentParams,
      search: debouncedSearchQuery || undefined,
    };

    fetchItemsInternal(paramsWithDebouncedSearch);
  }, [
    debouncedSearchQuery,
    currentParams.page,
    currentParams.perPage,
    fetchItemsInternal,
  ]);

  const refetch = useCallback(() => {
    const paramsWithDebouncedSearch = {
      ...currentParams,
      search: debouncedSearchQuery || undefined,
    };
    fetchItemsInternal(paramsWithDebouncedSearch);
  }, [currentParams, debouncedSearchQuery, fetchItemsInternal]);

  // Initial load
  useEffect(() => {
    fetchItems(initialParams);
  }, [fetchItems, initialParams]);

  return {
    ...state,
    fetchItems,
    refetch,
  };
};
