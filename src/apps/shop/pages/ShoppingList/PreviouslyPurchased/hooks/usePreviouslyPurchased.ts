import { useCallback, useEffect, useState } from 'react';
import {
  fetchPreviouslyPurchased,
  type FetchPreviouslyPurchasedParams,
} from '../services/fetchPreviouslyPurchased';
import { PreviouslyPurchasedItemType } from '@/types';

type UsePreviouslyPurchasedState = {
  items: PreviouslyPurchasedItemType[];
  total: number;
  isLoading: boolean;
  errorOnLoading: boolean;
};

type UsePreviouslyPurchasedReturn = UsePreviouslyPurchasedState & {
  fetchItems: (params: FetchPreviouslyPurchasedParams) => void;
  refetch: () => void;
};

export const usePreviouslyPurchased = (
  initialParams: FetchPreviouslyPurchasedParams = {},
): UsePreviouslyPurchasedReturn => {
  const [state, setState] = useState<UsePreviouslyPurchasedState>({
    items: [],
    total: 0,
    isLoading: false,
    errorOnLoading: false,
  });

  const [currentParams, setCurrentParams] =
    useState<FetchPreviouslyPurchasedParams>(initialParams);

  const fetchItems = useCallback((params: FetchPreviouslyPurchasedParams) => {
    setCurrentParams(params);

    fetchPreviouslyPurchased({
      params,
      beforeStart: () => {
        setState((prev) => ({
          ...prev,
          isLoading: true,
          errorOnLoading: false,
        }));
      },
      onSuccess: (data) => {
        setState((prev) => ({
          ...prev,
          items: data.items,
          total: data.total,
        }));
      },
      onError: () => {
        setState((prev) => ({ ...prev, errorOnLoading: true }));
      },
      afterDone: () => {
        setState((prev) => ({ ...prev, isLoading: false }));
      },
    });
  }, []);

  const refetch = useCallback(() => {
    fetchItems(currentParams);
  }, [fetchItems, currentParams]);

  useEffect(() => {
    fetchItems(initialParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    ...state,
    fetchItems,
    refetch,
  };
};
