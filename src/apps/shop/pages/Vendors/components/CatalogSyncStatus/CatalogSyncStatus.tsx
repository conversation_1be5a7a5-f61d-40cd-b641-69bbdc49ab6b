import { CatalogSyncStatusType, VendorType } from '@/types';
import { ReactNode } from 'react';
import RunningIcon from './assets/running.svg?react';
import SuccessIcon from './assets/success.svg?react';
import FailIcon from './assets/fail.svg?react';
import { LastUpdated } from '../LastUpdated/LastUpdated';

const syncStatusConfigs: Record<
  CatalogSyncStatusType,
  {
    label: string;
    color: string;
    icon: ReactNode;
  }
> = {
  failed: {
    label: 'Sync Fail',
    color: 'text-[#F14336]',
    icon: <FailIcon />,
  },
  pending: {
    label: 'Syncing Products',
    color: 'text-[#57ABB0]',
    icon: <RunningIcon />,
  },
  running: {
    label: 'Syncing Products',
    color: 'text-[#57ABB0]',
    icon: <RunningIcon />,
  },
  succeeded: {
    label: 'Synced',
    color: 'text-[#6AA555]',
    icon: <SuccessIcon />,
  },
};

export const CatalogSyncStatus = ({
  lastProductCatalogSync,
  hasNoProductCatalogSync,
}: {
  lastProductCatalogSync: VendorType['lastProductCatalogSync'];
  hasNoProductCatalogSync: boolean;
}) => {
  return (
    <div>
      {lastProductCatalogSync ? (
        <div className="flex flex-col items-end">
          <Status status={lastProductCatalogSync.status} />
          {lastProductCatalogSync.status === 'succeeded' &&
            lastProductCatalogSync.updatedAt && (
              <LastUpdated updatedAt={lastProductCatalogSync.updatedAt} />
            )}
        </div>
      ) : hasNoProductCatalogSync ? (
        <Status status="succeeded" />
      ) : (
        '-'
      )}
    </div>
  );
};

export const Status = ({ status }: { status: CatalogSyncStatusType }) => {
  // TODO: Notify Sentry when an untracked status appears
  const { color, label, icon } = syncStatusConfigs[status] || {};

  return (
    <div className="flex items-center gap-1">
      {icon}
      <p className={`text-sm font-medium ${color}`}>{label}</p>
    </div>
  );
};
