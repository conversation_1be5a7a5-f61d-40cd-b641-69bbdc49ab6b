<?php

declare(strict_types=1);

use App\Support\PhoneNumberFormatter;

it('formats 10-digit US phone numbers to E.164', function (string $input, string $expected) {
    expect(PhoneNumberFormatter::formatToE164($input))->toBe($expected);
})->with([
    'standard format' => ['(*************', '+16166626230'],
    'dash format' => ['************', '+16166626230'],
    'space format' => ['************', '+16166626230'],
    'no formatting' => ['6166626230', '+16166626230'],
    'dots format' => ['************', '+16166626230'],
]);

it('formats 11-digit US phone numbers starting with 1', function (string $input, string $expected) {
    expect(PhoneNumberFormatter::formatToE164($input))->toBe($expected);
})->with([
    'with parentheses' => ['(1) ************', '+16166626230'],
    'with dash' => ['1-************', '+16166626230'],
    'no formatting' => ['16166626230', '+16166626230'],
]);

it('handles already formatted E.164 numbers', function (string $input, string $expected) {
    expect(PhoneNumberFormatter::formatToE164($input))->toBe($expected);
})->with([
    'already E.164' => ['+16166626230', '+16166626230'],
    'E.164 with spaces' => ['+1 ************', '+16166626230'],
    'E.164 with dashes' => ['+1-************', '+16166626230'],
]);

it('returns null for invalid phone numbers', function (string $input) {
    expect(PhoneNumberFormatter::formatToE164($input))->toBeNull();
})->with([
    'too short' => ['123456789'],
    'too long' => ['123456789012'],
    'starts with wrong digit' => ['26166626230'],
    'contains letters' => ['616-662-ABCD'],
    'empty string' => [''],
    'only special characters' => ['() -'],
]);

it('validates US phone numbers correctly', function (string $input, bool $expected) {
    expect(PhoneNumberFormatter::isValidUSPhoneNumber($input))->toBe($expected);
})->with([
    'valid 10-digit' => ['(*************', true],
    'valid 11-digit' => ['16166626230', true],
    'valid E.164' => ['+16166626230', true],
    'invalid too short' => ['123456789', false],
    'invalid too long' => ['123456789012', false],
    'invalid format' => ['616-662-ABCD', false],
]);
