<?php

declare(strict_types=1);

use App\Support\StateNormalizer;

test('normalizes full state names to abbreviations', function () {
    expect(StateNormalizer::normalize('California'))->toBe('CA');
    expect(StateNormalizer::normalize('california'))->toBe('CA');
    expect(StateNormalizer::normalize('CALIFORNIA'))->toBe('CA');
    expect(StateNormalizer::normalize('New York'))->toBe('NY');
    expect(StateNormalizer::normalize('new york'))->toBe('NY');
    expect(StateNormalizer::normalize('North Carolina'))->toBe('NC');
    expect(StateNormalizer::normalize('District of Columbia'))->toBe('DC');
});

test('normalizes state abbreviations to uppercase', function () {
    expect(StateNormalizer::normalize('ca'))->toBe('CA');
    expect(StateNormalizer::normalize('CA'))->toBe('CA');
    expect(StateNormalizer::normalize('ny'))->toBe('NY');
    expect(StateNormalizer::normalize('NY'))->toBe('NY');
    expect(StateNormalizer::normalize('fl'))->toBe('FL');
    expect(StateNormalizer::normalize('tx'))->toBe('TX');
});

test('handles territories correctly', function () {
    expect(StateNormalizer::normalize('Puerto Rico'))->toBe('PR');
    expect(StateNormalizer::normalize('Guam'))->toBe('GU');
    expect(StateNormalizer::normalize('Virgin Islands'))->toBe('VI');
    expect(StateNormalizer::normalize('American Samoa'))->toBe('AS');
});

test('handles null and empty strings', function () {
    expect(StateNormalizer::normalize(null))->toBeNull();
    expect(StateNormalizer::normalize(''))->toBeNull();
    expect(StateNormalizer::normalize('   '))->toBeNull();
});

test('returns original value for unrecognized states', function () {
    expect(StateNormalizer::normalize('Invalid State'))->toBe('Invalid State');
    expect(StateNormalizer::normalize('XX'))->toBe('XX');
});

test('validates state abbreviations correctly', function () {
    expect(StateNormalizer::isValid('CA'))->toBeTrue();
    expect(StateNormalizer::isValid('ca'))->toBeTrue();
    expect(StateNormalizer::isValid('California'))->toBeTrue();
    expect(StateNormalizer::isValid('NY'))->toBeTrue();
    expect(StateNormalizer::isValid('New York'))->toBeTrue();
    expect(StateNormalizer::isValid('PR'))->toBeTrue();
});

test('rejects invalid states', function () {
    expect(StateNormalizer::isValid('XX'))->toBeFalse();
    expect(StateNormalizer::isValid('Invalid State'))->toBeFalse();
    expect(StateNormalizer::isValid(''))->toBeFalse();
    expect(StateNormalizer::isValid(null))->toBeFalse();
});

test('handles all 50 states', function () {
    $states = [
        'Alabama' => 'AL',
        'Alaska' => 'AK',
        'Arizona' => 'AZ',
        'Arkansas' => 'AR',
        'California' => 'CA',
        'Colorado' => 'CO',
        'Connecticut' => 'CT',
        'Delaware' => 'DE',
        'Florida' => 'FL',
        'Georgia' => 'GA',
        'Hawaii' => 'HI',
        'Idaho' => 'ID',
        'Illinois' => 'IL',
        'Indiana' => 'IN',
        'Iowa' => 'IA',
        'Kansas' => 'KS',
        'Kentucky' => 'KY',
        'Louisiana' => 'LA',
        'Maine' => 'ME',
        'Maryland' => 'MD',
        'Massachusetts' => 'MA',
        'Michigan' => 'MI',
        'Minnesota' => 'MN',
        'Mississippi' => 'MS',
        'Missouri' => 'MO',
        'Montana' => 'MT',
        'Nebraska' => 'NE',
        'Nevada' => 'NV',
        'New Hampshire' => 'NH',
        'New Jersey' => 'NJ',
        'New Mexico' => 'NM',
        'New York' => 'NY',
        'North Carolina' => 'NC',
        'North Dakota' => 'ND',
        'Ohio' => 'OH',
        'Oklahoma' => 'OK',
        'Oregon' => 'OR',
        'Pennsylvania' => 'PA',
        'Rhode Island' => 'RI',
        'South Carolina' => 'SC',
        'South Dakota' => 'SD',
        'Tennessee' => 'TN',
        'Texas' => 'TX',
        'Utah' => 'UT',
        'Vermont' => 'VT',
        'Virginia' => 'VA',
        'Washington' => 'WA',
        'West Virginia' => 'WV',
        'Wisconsin' => 'WI',
        'Wyoming' => 'WY',
    ];

    foreach ($states as $fullName => $abbreviation) {
        expect(StateNormalizer::normalize($fullName))->toBe($abbreviation);
    }
});
