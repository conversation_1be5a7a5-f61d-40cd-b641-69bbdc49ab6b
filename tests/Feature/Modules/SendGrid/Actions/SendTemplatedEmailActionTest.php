<?php

declare(strict_types=1);

use App\Modules\SendGrid\Actions\SendTemplatedEmailAction;
use App\Modules\SendGrid\Contracts\SendGridClientInterface;
use Illuminate\Support\Facades\Log;
use SendGrid\Response as SendGridResponse;

use function Pest\Laravel\mock;

it('sends templated email successfully with 202 status', function () {
    $mockResponse = mock(SendGridResponse::class);
    $mockResponse->shouldReceive('statusCode')
        ->andReturn(202);

    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldReceive('sendTemplatedEmail')
        ->with('template-123', [
            'to' => [
                [
                    'email' => '<EMAIL>',
                    'name' => 'Test User',
                    'template_data' => ['var1' => 'value1'],
                ],
            ],
        ])
        ->once()
        ->andReturn($mockResponse);

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', [
        'to' => [
            [
                'email' => '<EMAIL>',
                'name' => 'Test User',
                'template_data' => ['var1' => 'value1'],
            ],
        ],
    ]);

    expect($result)->toBeTrue();
});

it('sends templated email to multiple recipients', function () {
    $mockResponse = mock(SendGridResponse::class);
    $mockResponse->shouldReceive('statusCode')
        ->andReturn(202);

    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldReceive('sendTemplatedEmail')
        ->with('template-123', [
            'to' => [
                ['email' => '<EMAIL>', 'name' => 'User One'],
                ['email' => '<EMAIL>', 'name' => 'User Two'],
            ],
        ])
        ->once()
        ->andReturn($mockResponse);

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', [
        'to' => [
            ['email' => '<EMAIL>', 'name' => 'User One'],
            ['email' => '<EMAIL>', 'name' => 'User Two'],
        ],
    ]);

    expect($result)->toBeTrue();
});

it('sends templated email with cc and bcc recipients', function () {
    $mockResponse = mock(SendGridResponse::class);
    $mockResponse->shouldReceive('statusCode')
        ->andReturn(202);

    $params = [
        'to' => [
            ['email' => '<EMAIL>', 'name' => 'To User'],
        ],
        'cc' => [
            ['email' => '<EMAIL>', 'name' => 'CC User'],
        ],
        'bcc' => [
            ['email' => '<EMAIL>', 'name' => 'BCC User'],
        ],
    ];

    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldReceive('sendTemplatedEmail')
        ->with('template-123', $params)
        ->once()
        ->andReturn($mockResponse);

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', $params);

    expect($result)->toBeTrue();
});

it('sends templated email with custom from address', function () {
    $mockResponse = mock(SendGridResponse::class);
    $mockResponse->shouldReceive('statusCode')
        ->andReturn(202);

    $params = [
        'to' => [
            ['email' => '<EMAIL>', 'name' => 'Test User'],
        ],
        'from' => [
            'email' => '<EMAIL>',
            'name' => 'Custom Sender',
        ],
    ];

    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldReceive('sendTemplatedEmail')
        ->with('template-123', $params)
        ->once()
        ->andReturn($mockResponse);

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', $params);

    expect($result)->toBeTrue();
});

it('returns false when SendGrid returns non-202 status', function () {
    $mockResponse = mock(SendGridResponse::class);
    $mockResponse->shouldReceive('statusCode')
        ->andReturn(400);
    $mockResponse->shouldReceive('body')
        ->andReturn('Bad Request');

    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldReceive('sendTemplatedEmail')
        ->with('template-123', [
            'to' => [
                ['email' => '<EMAIL>', 'name' => 'Test User'],
            ],
        ])
        ->once()
        ->andReturn($mockResponse);

    Log::shouldReceive('error')
        ->with('SendGrid email failed', [
            'template_id' => 'template-123',
            'status_code' => 400,
            'response_body' => 'Bad Request',
        ])
        ->once();

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', [
        'to' => [
            ['email' => '<EMAIL>', 'name' => 'Test User'],
        ],
    ]);

    expect($result)->toBeFalse();
});

it('returns false and logs error when exception is thrown', function () {
    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldReceive('sendTemplatedEmail')
        ->andThrow(new Exception('SendGrid API Error'));

    Log::shouldReceive('error')
        ->with('Failed to send SendGrid email', Mockery::on(function ($context) {
            return $context['template_id'] === 'template-123'
                && $context['error'] === 'SendGrid API Error'
                && isset($context['trace']);
        }))
        ->once();

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', [
        'to' => [
            ['email' => '<EMAIL>', 'name' => 'Test User'],
        ],
    ]);

    expect($result)->toBeFalse();
});

it('returns false and logs error when to parameter is missing', function () {
    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldNotReceive('sendTemplatedEmail');

    Log::shouldReceive('error')
        ->with('Failed to send SendGrid email', Mockery::on(function ($context) {
            return $context['template_id'] === 'template-123'
                && str_contains($context['error'], 'Recipients (to) parameter is required');
        }))
        ->once();

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', []);

    expect($result)->toBeFalse();
});

it('returns false and logs error when to parameter is empty', function () {
    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldNotReceive('sendTemplatedEmail');

    Log::shouldReceive('error')
        ->with('Failed to send SendGrid email', Mockery::on(function ($context) {
            return $context['template_id'] === 'template-123'
                && str_contains($context['error'], 'Recipients (to) parameter is required');
        }))
        ->once();

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', ['to' => []]);

    expect($result)->toBeFalse();
});

it('returns false and logs error when to parameter is not an array', function () {
    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldNotReceive('sendTemplatedEmail');

    Log::shouldReceive('error')
        ->with('Failed to send SendGrid email', Mockery::on(function ($context) {
            return $context['template_id'] === 'template-123'
                && $context['error'] === 'Recipients (to) parameter must be an array';
        }))
        ->once();

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', ['to' => 'invalid']);

    expect($result)->toBeFalse();
});

it('returns false and logs error when recipient is missing email address', function () {
    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldNotReceive('sendTemplatedEmail');

    Log::shouldReceive('error')
        ->with('Failed to send SendGrid email', Mockery::on(function ($context) {
            return $context['template_id'] === 'template-123'
                && $context['error'] === 'Each recipient must have an email address';
        }))
        ->once();

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', [
        'to' => [
            ['name' => 'Test User'],
        ],
    ]);

    expect($result)->toBeFalse();
});

it('returns false and logs error when one of multiple recipients is missing email address', function () {
    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldNotReceive('sendTemplatedEmail');

    Log::shouldReceive('error')
        ->with('Failed to send SendGrid email', Mockery::on(function ($context) {
            return $context['template_id'] === 'template-123'
                && $context['error'] === 'Each recipient must have an email address';
        }))
        ->once();

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-123', [
        'to' => [
            ['email' => '<EMAIL>', 'name' => 'Valid User'],
            ['name' => 'Invalid User'],
        ],
    ]);

    expect($result)->toBeFalse();
});

it('logs error with correct context when SendGrid returns error status', function () {
    $mockResponse = mock(SendGridResponse::class);
    $mockResponse->shouldReceive('statusCode')
        ->andReturn(500);
    $mockResponse->shouldReceive('body')
        ->andReturn('Internal Server Error');

    $mockClient = mock(SendGridClientInterface::class);
    $mockClient->shouldReceive('sendTemplatedEmail')
        ->andReturn($mockResponse);

    Log::shouldReceive('error')
        ->with('SendGrid email failed', [
            'template_id' => 'template-456',
            'status_code' => 500,
            'response_body' => 'Internal Server Error',
        ])
        ->once();

    $action = new SendTemplatedEmailAction($mockClient);
    $result = $action->handle('template-456', [
        'to' => [
            ['email' => '<EMAIL>', 'name' => 'Test User'],
        ],
    ]);

    expect($result)->toBeFalse();
});
