<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Models\MoniteEntity;
use App\Modules\Monite\Services\MoniteEntityService;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Http\Client\Response;

use function Pest\Laravel\mock;

beforeEach(function () {
    $this->clinic = Clinic::factory()->create([
        'monite_entity_id' => null,
    ]);

    // Enable Monite for the clinic
    $featureFlagService = app(MoniteFeatureFlagService::class);
    $featureFlagService->enable($this->clinic);
});

test('creates entity and syncs details to local database', function () {
    $entityId = 'test-entity-id-123';
    $createdAt = now()->subDays(1)->toIso8601String();
    $updatedAt = now()->toIso8601String();

    $entityCreationData = [
        'id' => $entityId,
        'type' => 'organization',
        'status' => 'new',
        'email' => '<EMAIL>',
        'created_at' => $createdAt,
        'updated_at' => $updatedAt,
    ];

    $entityDetails = [
        'id' => $entityId,
        'type' => 'organization',
        'status' => 'new',
        'email' => '<EMAIL>',
        'phone' => '+16166626230',
        'tax_id' => '12-3456789',
        'created_at' => $createdAt,
        'updated_at' => $updatedAt,
        'address' => [
            'country' => 'US',
            'city' => 'San Francisco',
            'state' => 'CA',
            'postal_code' => '94102',
            'line1' => '123 Main St',
        ],
        'organization' => [
            'legal_name' => $this->clinic->name,
        ],
    ];

    $onboardingRequirements = [
        'data' => [
            [
                'payment_method' => 'card',
                'requirements' => [
                    'currently_due' => ['business_profile.url'],
                    'eventually_due' => [],
                    'past_due' => [],
                    'pending_verification' => [],
                    'current_deadline' => null,
                ],
                'requirements_errors' => [],
                'verification_errors' => [],
                'verification_status' => 'pending',
                'disabled_reason' => null,
            ],
        ],
    ];

    // Mock responses
    $mockCreateResponse = mock(Response::class);
    $mockCreateResponse->shouldReceive('successful')->andReturn(true);
    $mockCreateResponse->shouldReceive('json')->andReturn($entityCreationData);

    $mockEntityResponse = mock(Response::class);
    $mockEntityResponse->shouldReceive('successful')->andReturn(true);
    $mockEntityResponse->shouldReceive('json')->andReturn($entityDetails);

    $mockRequirementsResponse = mock(Response::class);
    $mockRequirementsResponse->shouldReceive('successful')->andReturn(true);
    $mockRequirementsResponse->shouldReceive('json')->andReturn($onboardingRequirements);

    $paymentMethodsResponse = [
        'data' => [
            [
                'direction' => 'receive',
                'name' => 'US ACH',
                'status' => 'active',
                'type' => 'us_ach',
            ],
        ],
    ];

    $mockPaymentMethodsResponse = mock(Response::class);
    $mockPaymentMethodsResponse->shouldReceive('successful')->andReturn(true);
    $mockPaymentMethodsResponse->shouldReceive('json')->andReturn($paymentMethodsResponse);

    // Mock the Monite API client
    $mockClient = mock(MoniteApiClientInterface::class);

    // Mock entity creation
    $mockClient->shouldReceive('post')
        ->once()
        ->with('/entities', Mockery::any())
        ->andReturn($mockCreateResponse);

    // Mock fetching entity details
    $mockClient->shouldReceive('withEntityId')
        ->with($entityId)
        ->andReturnSelf();

    $mockClient->shouldReceive('get')
        ->with('/entities/me')
        ->andReturn($mockEntityResponse);

    // Mock fetching onboarding requirements
    $mockClient->shouldReceive('get')
        ->with('/onboarding_requirements')
        ->andReturn($mockRequirementsResponse);

    // Mock enabling payment methods
    $mockClient->shouldReceive('put')
        ->with("/entities/{$entityId}/payment_methods", Mockery::any())
        ->andReturn($mockPaymentMethodsResponse);

    $service = new MoniteEntityService($mockClient);
    $result = $service->createEntityForClinic($this->clinic);

    // Assert entity ID was returned
    expect($result)->toBe($entityId);

    // Assert clinic was updated with entity ID
    $this->clinic->refresh();
    expect($this->clinic->monite_entity_id)->toBe($entityId);

    // Assert MoniteEntity record was created
    $moniteEntity = MoniteEntity::where('monite_entity_id', $entityId)->first();
    expect($moniteEntity)->not->toBeNull();
    expect($moniteEntity->clinic_id)->toBe($this->clinic->id);
    expect($moniteEntity->status)->toBe('new');
    expect($moniteEntity->type)->toBe('organization');
    expect($moniteEntity->email)->toBe('<EMAIL>');
    expect($moniteEntity->entity_data)->toBeArray();
    expect($moniteEntity->entity_data['id'])->toBe($entityId);
    expect($moniteEntity->onboarding_requirements)->toBeArray();
    expect($moniteEntity->onboarding_requirements['payment_method'])->toBe('card');
    expect($moniteEntity->onboarding_requirements['verification_status'])->toBe('pending');
});

test('creates entity even if syncing details fails', function () {
    $entityId = 'test-entity-id-456';

    $entityCreationData = [
        'id' => $entityId,
        'type' => 'organization',
        'status' => 'new',
        'email' => '<EMAIL>',
        'created_at' => now()->toIso8601String(),
        'updated_at' => now()->toIso8601String(),
    ];

    // Mock responses
    $mockCreateResponse = mock(Response::class);
    $mockCreateResponse->shouldReceive('successful')->andReturn(true);
    $mockCreateResponse->shouldReceive('json')->andReturn($entityCreationData);

    $mockEntityResponse = mock(Response::class);
    $mockEntityResponse->shouldReceive('successful')->andReturn(false);
    $mockEntityResponse->shouldReceive('body')->andReturn('Internal Server Error');

    $paymentMethodsResponse = [
        'data' => [
            [
                'direction' => 'receive',
                'name' => 'US ACH',
                'status' => 'active',
                'type' => 'us_ach',
            ],
        ],
    ];

    $mockPaymentMethodsResponse = mock(Response::class);
    $mockPaymentMethodsResponse->shouldReceive('successful')->andReturn(true);
    $mockPaymentMethodsResponse->shouldReceive('json')->andReturn($paymentMethodsResponse);

    // Mock the Monite API client
    $mockClient = mock(MoniteApiClientInterface::class);

    // Mock entity creation (succeeds)
    $mockClient->shouldReceive('post')
        ->once()
        ->with('/entities', Mockery::any())
        ->andReturn($mockCreateResponse);

    // Mock fetching entity details (fails)
    $mockClient->shouldReceive('withEntityId')
        ->times(2) // Called for sync (fails) and payment methods (succeeds)
        ->with($entityId)
        ->andReturnSelf();

    $mockClient->shouldReceive('get')
        ->once()
        ->with('/entities/me')
        ->andReturn($mockEntityResponse);

    // Mock enabling payment methods (succeeds despite sync failure)
    $mockClient->shouldReceive('put')
        ->with("/entities/{$entityId}/payment_methods", Mockery::any())
        ->andReturn($mockPaymentMethodsResponse);

    $service = new MoniteEntityService($mockClient);
    $result = $service->createEntityForClinic($this->clinic);

    // Assert entity ID was still returned
    expect($result)->toBe($entityId);

    // Assert clinic was updated with entity ID
    $this->clinic->refresh();
    expect($this->clinic->monite_entity_id)->toBe($entityId);

    // Assert MoniteEntity record was NOT created (sync failed)
    $moniteEntity = MoniteEntity::where('monite_entity_id', $entityId)->first();
    expect($moniteEntity)->toBeNull();
});
