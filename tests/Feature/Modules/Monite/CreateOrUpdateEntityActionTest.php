<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Modules\Monite\Actions\CreateOrUpdateEntityAction;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Services\MoniteEntityService;
use App\Modules\Monite\Services\MoniteEntitySettingsService;

beforeEach(function () {
    $this->clinic = Clinic::factory()->create([
        'monite_entity_id' => null,
    ]);

    $this->entityService = mock(MoniteEntityService::class);
    $this->settingsService = mock(MoniteEntitySettingsService::class);
    $this->action = new CreateOrUpdateEntityAction($this->entityService, $this->settingsService);
});

it('creates entity and configures settings successfully', function () {
    $entityId = 'test-entity-id';

    $this->entityService
        ->shouldReceive('createEntityForClinic')
        ->with($this->clinic)
        ->andReturn($entityId);

    $this->settingsService
        ->shouldReceive('updateSettings')
        ->with($this->clinic, Mockery::type('array'), 'configure entity settings')
        ->once();

    $result = $this->action->execute($this->clinic);

    expect($result)->toBe($entityId);
    expect($this->clinic->fresh()->monite_entity_id)->toBe($entityId);
});

it('handles settings configuration failure gracefully', function () {
    $entityId = 'test-entity-id';

    $this->entityService
        ->shouldReceive('createEntityForClinic')
        ->with($this->clinic)
        ->andReturn($entityId);

    $this->settingsService
        ->shouldReceive('updateSettings')
        ->with($this->clinic, Mockery::type('array'), 'configure entity settings')
        ->andThrow(new MoniteApiException('Settings configuration failed'));

    $result = $this->action->execute($this->clinic);

    // Should still return the entity ID even if settings configuration fails
    expect($result)->toBe($entityId);
    expect($this->clinic->fresh()->monite_entity_id)->toBe($entityId);
});

it('returns null when entity creation fails', function () {
    $this->entityService
        ->shouldReceive('createEntityForClinic')
        ->with($this->clinic)
        ->andReturn(null);

    $this->settingsService
        ->shouldNotReceive('enableDuplicateCancellation');

    $result = $this->action->execute($this->clinic);

    expect($result)->toBeNull();
    expect($this->clinic->fresh()->monite_entity_id)->toBeNull();
});

it('updates existing entity without configuring settings', function () {
    $existingEntityId = 'existing-entity-id';
    $this->clinic->update(['monite_entity_id' => $existingEntityId]);

    $this->entityService
        ->shouldReceive('updateEntityForClinic')
        ->with($this->clinic)
        ->once();

    $this->entityService
        ->shouldNotReceive('createEntityForClinic');

    $this->settingsService
        ->shouldNotReceive('enableDuplicateCancellation');

    $result = $this->action->execute($this->clinic);

    expect($result)->toBe($existingEntityId);
});

it('handles entity creation exception', function () {
    $this->entityService
        ->shouldReceive('createEntityForClinic')
        ->with($this->clinic)
        ->andThrow(new MoniteApiException('Entity creation failed'));

    $this->settingsService
        ->shouldNotReceive('enableDuplicateCancellation');

    $result = $this->action->execute($this->clinic);

    expect($result)->toBeNull();
    expect($this->clinic->fresh()->monite_entity_id)->toBeNull();
});

it('handles entity update exception', function () {
    $existingEntityId = 'existing-entity-id';
    $this->clinic->update(['monite_entity_id' => $existingEntityId]);

    $this->entityService
        ->shouldReceive('updateEntityForClinic')
        ->with($this->clinic)
        ->andThrow(new MoniteApiException('Entity update failed'));

    $this->settingsService
        ->shouldNotReceive('enableDuplicateCancellation');

    $result = $this->action->execute($this->clinic);

    expect($result)->toBeNull();
});
