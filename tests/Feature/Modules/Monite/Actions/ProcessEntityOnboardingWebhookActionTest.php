<?php

declare(strict_types=1);

use App\Enums\MoniteWebhookEventStatus;
use App\Models\Clinic;
use App\Modules\Monite\Actions\ProcessEntityOnboardingWebhookAction;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Models\MoniteEntity;
use App\Modules\Monite\Models\MoniteWebhookEvent;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Http\Client\Response;

use function Pest\Laravel\mock;

beforeEach(function () {
    $this->clinic = Clinic::factory()->create([
        'monite_entity_id' => 'entity-123',
    ]);

    $this->featureFlagService = app(MoniteFeatureFlagService::class);
    $this->featureFlagService->enable($this->clinic);

    $this->webhookEvent = MoniteWebhookEvent::factory()->create([
        'event_id' => 'evt-123',
        'event_type' => 'entity.onboarding_requirements.updated',
        'entity_id' => 'entity-123',
        'object_type' => 'entity',
        'payload' => [
            'id' => 'evt-123',
            'action' => 'entity.onboarding_requirements.updated',
            'entity_id' => 'entity-123',
            'object_type' => 'entity',
            'object' => ['id' => 'entity-123'],
        ],
    ]);
});

it('processes entity onboarding webhook successfully', function () {
    $entityDetails = [
        'id' => 'entity-123',
        'status' => 'active',
        'type' => 'organization',
        'email' => '<EMAIL>',
        'created_at' => '2025-09-30T10:00:00.000000Z',
        'updated_at' => '2025-09-30T10:30:00.000000Z',
    ];

    $onboardingRequirements = [
        'data' => [
            [
                'payment_method' => 'card',
                'requirements' => [
                    'currently_due' => [],
                    'eventually_due' => [],
                    'past_due' => [],
                    'pending_verification' => [],
                ],
                'verification_status' => 'enabled',
            ],
        ],
    ];

    $mockEntityResponse = mock(Response::class);
    $mockEntityResponse->shouldReceive('json')
        ->andReturn($entityDetails);

    $mockRequirementsResponse = mock(Response::class);
    $mockRequirementsResponse->shouldReceive('successful')
        ->andReturn(true);
    $mockRequirementsResponse->shouldReceive('json')
        ->andReturn($onboardingRequirements);

    $mockApiClient = mock(MoniteApiClientInterface::class);
    $mockApiClient->shouldReceive('withEntityId')
        ->with('entity-123')
        ->andReturnSelf();
    $mockApiClient->shouldReceive('get')
        ->with('/entities/me')
        ->andReturn($mockEntityResponse);
    $mockApiClient->shouldReceive('get')
        ->with('/onboarding_requirements')
        ->andReturn($mockRequirementsResponse);

    $action = new ProcessEntityOnboardingWebhookAction($mockApiClient);
    $action->execute($this->webhookEvent, $this->clinic);

    // Check MoniteEntity was created with correct data
    $moniteEntity = MoniteEntity::where('monite_entity_id', $this->clinic->monite_entity_id)->first();
    expect($moniteEntity)
        ->not->toBeNull()
        ->status->toBe('active')
        ->type->toBe('organization')
        ->email->toBe('<EMAIL>');

    expect($this->webhookEvent->fresh()->status)->toBe(MoniteWebhookEventStatus::PROCESSED);
});

it('updates clinic with pending status', function () {
    $entityDetails = [
        'id' => 'entity-123',
        'status' => 'pending',
        'type' => 'organization',
        'email' => '<EMAIL>',
        'created_at' => '2025-09-30T10:00:00.000000Z',
        'updated_at' => '2025-09-30T10:30:00.000000Z',
    ];

    $onboardingRequirements = [
        'data' => [
            [
                'payment_method' => 'card',
                'requirements' => [
                    'currently_due' => ['business_details', 'bank_account'],
                    'eventually_due' => [],
                    'past_due' => [],
                    'pending_verification' => [],
                ],
                'verification_status' => 'pending',
            ],
        ],
    ];

    $mockEntityResponse = mock(Response::class);
    $mockEntityResponse->shouldReceive('json')
        ->andReturn($entityDetails);

    $mockRequirementsResponse = mock(Response::class);
    $mockRequirementsResponse->shouldReceive('successful')
        ->andReturn(true);
    $mockRequirementsResponse->shouldReceive('json')
        ->andReturn($onboardingRequirements);

    $mockApiClient = mock(MoniteApiClientInterface::class);
    $mockApiClient->shouldReceive('withEntityId')
        ->with('entity-123')
        ->andReturnSelf();
    $mockApiClient->shouldReceive('get')
        ->with('/entities/me')
        ->andReturn($mockEntityResponse);
    $mockApiClient->shouldReceive('get')
        ->with('/onboarding_requirements')
        ->andReturn($mockRequirementsResponse);

    $action = new ProcessEntityOnboardingWebhookAction($mockApiClient);
    $action->execute($this->webhookEvent, $this->clinic);

    // Check MoniteEntity was created with pending status
    $moniteEntity = MoniteEntity::where('monite_entity_id', $this->clinic->monite_entity_id)->first();
    expect($moniteEntity)
        ->not->toBeNull()
        ->status->toBe('pending');

    expect($this->webhookEvent->fresh()->status)->toBe(MoniteWebhookEventStatus::PROCESSED);
});

it('marks webhook as ignored when Monite feature is disabled', function () {
    $this->featureFlagService->disable($this->clinic);

    $mockApiClient = mock(MoniteApiClientInterface::class);
    $mockApiClient->shouldNotReceive('withEntityId');
    $mockApiClient->shouldNotReceive('get');

    $action = new ProcessEntityOnboardingWebhookAction($mockApiClient);
    $action->execute($this->webhookEvent, $this->clinic);

    expect($this->webhookEvent->fresh()->status)->toBe(MoniteWebhookEventStatus::IGNORED);
});

it('marks webhook as failed when API call fails', function () {
    $mockApiClient = mock(MoniteApiClientInterface::class);
    $mockApiClient->shouldReceive('withEntityId')
        ->with('entity-123')
        ->andReturnSelf();
    $mockApiClient->shouldReceive('get')
        ->with('/entities/me')
        ->andThrow(new Exception('API Error'));

    $action = new ProcessEntityOnboardingWebhookAction($mockApiClient);

    expect(fn () => $action->execute($this->webhookEvent, $this->clinic))
        ->toThrow(Exception::class);

    expect($this->webhookEvent->fresh()->status)->toBe(MoniteWebhookEventStatus::FAILED);
});
