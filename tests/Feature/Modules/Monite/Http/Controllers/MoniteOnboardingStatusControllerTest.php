<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Modules\Monite\Models\MoniteEntity;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->clinic = Clinic::factory()->create([
        'monite_entity_id' => 'test-entity-123',
    ]);

    // Create MoniteEntity with onboarding data
    $this->moniteEntity = MoniteEntity::factory()->create([
        'monite_entity_id' => 'test-entity-123',
        'clinic_id' => $this->clinic->id,
        'status' => 'pending',
        'onboarding_requirements' => [
            'payment_method' => 'card',
            'requirements' => [
                'currently_due' => ['business_details', 'bank_account'],
                'eventually_due' => [],
                'past_due' => [],
                'pending_verification' => [],
            ],
        ],
    ]);

    $this->clinic->users()->attach($this->user);

    $this->featureFlagService = app(MoniteFeatureFlagService::class);
    $this->featureFlagService->enable($this->clinic);

    Sanctum::actingAs($this->user);
});

it('returns onboarding status for authenticated user with clinic', function () {
    $response = $this->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/monite/onboarding-status');

    $response->assertSuccessful()
        ->assertJson([
            'entityId' => 'test-entity-123',
            'entityStatus' => 'pending',
            'verificationStatus' => null,
            'isOnboardingComplete' => false,
        ])
        ->assertJsonStructure([
            'entityId',
            'entityStatus',
            'verificationStatus',
            'onboardingRequirements',
            'lastSyncedAt',
            'isOnboardingComplete',
        ]);
});

it('returns onboarding complete when verification status is enabled', function () {
    $this->moniteEntity->update([
        'status' => 'active',
        'onboarding_requirements' => [
            'verification_status' => 'enabled',
            'disabled_reason' => null,
            'payment_method' => 'card',
            'requirements' => [
                'currently_due' => [],
                'eventually_due' => [],
                'past_due' => [],
                'pending_verification' => [],
            ],
        ],
    ]);

    $response = $this->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/monite/onboarding-status');

    $response->assertSuccessful()
        ->assertJson([
            'entityStatus' => 'active',
            'verificationStatus' => 'enabled',
            'isOnboardingComplete' => true,
        ]);
});

it('returns 403 when Monite integration is not enabled for clinic', function () {
    $this->featureFlagService->disable($this->clinic);

    $response = $this->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/monite/onboarding-status');

    $response->assertForbidden()
        ->assertJson([
            'error' => 'Monite integration is not enabled for this clinic',
            'code' => 'MONITE_NOT_ENABLED',
        ]);
});

it('returns 404 when clinic has no Monite entity', function () {
    $this->clinic->update(['monite_entity_id' => null]);

    $response = $this->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/monite/onboarding-status');

    $response->assertNotFound()
        ->assertJson([
            'error' => 'No Monite entity found for this clinic',
            'code' => 'MONITE_ENTITY_NOT_FOUND',
        ]);
});

it('requires authentication', function () {
    $this->withoutMiddleware(App\Http\Middleware\Authenticate::class);

    $response = $this->getJson('/api/monite/onboarding-status');

    $response->assertStatus(400);
});
