<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Services\MoniteEntitySettingsService;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Http\Client\Response;

beforeEach(function () {
    $this->clinic = Clinic::factory()->create([
        'monite_entity_id' => 'test-entity-id',
    ]);

    // Enable Monite feature for the clinic
    app(MoniteFeatureFlagService::class)->enable($this->clinic);

    $this->moniteClient = mock(MoniteApiClientInterface::class);
    $this->settingsService = new MoniteEntitySettingsService($this->moniteClient);
});

it('updates settings successfully', function () {
    $response = mock(Response::class);
    $response->shouldReceive('successful')->andReturn(true);

    $settingsData = [
        'payable' => [
            'allow_cancel_duplicates_automatically' => true,
            'approve_page_url' => 'https://example.com/approve',
        ],
    ];

    $this->moniteClient
        ->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $this->moniteClient
        ->shouldReceive('patch')
        ->with('/settings', $settingsData)
        ->andReturn($response);

    $this->settingsService->updateSettings($this->clinic, $settingsData, 'test operation');

    // If we get here without exception, the test passes
    expect(true)->toBeTrue();
});

it('updates settings with different data successfully', function () {
    $response = mock(Response::class);
    $response->shouldReceive('successful')->andReturn(true);

    $settingsData = [
        'payable' => [
            'allow_cancel_duplicates_automatically' => false,
            'approve_page_url' => 'https://example.com/approve',
        ],
    ];

    $this->moniteClient
        ->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $this->moniteClient
        ->shouldReceive('patch')
        ->with('/settings', $settingsData)
        ->andReturn($response);

    $this->settingsService->updateSettings($this->clinic, $settingsData, 'disable duplicate cancellation');

    // If we get here without exception, the test passes
    expect(true)->toBeTrue();
});

it('retrieves settings successfully', function () {
    $expectedSettings = [
        'payable' => [
            'allow_cancel_duplicates_automatically' => true,
        ],
    ];

    $response = mock(Response::class);
    $response->shouldReceive('successful')->andReturn(true);
    $response->shouldReceive('json')->andReturn($expectedSettings);

    $this->moniteClient
        ->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $this->moniteClient
        ->shouldReceive('get')
        ->with('/settings')
        ->andReturn($response);

    $settings = $this->settingsService->getSettings($this->clinic);

    expect($settings)->toBe($expectedSettings);
});

it('throws exception when clinic has no monite entity id for enable duplicate cancellation', function () {
    $clinicWithoutEntity = Clinic::factory()->create([
        'monite_entity_id' => null,
    ]);

    // Enable Monite feature for the clinic
    app(MoniteFeatureFlagService::class)->enable($clinicWithoutEntity);

    $settingsData = ['payable' => ['allow_cancel_duplicates_automatically' => true]];

    expect(fn () => $this->settingsService->updateSettings($clinicWithoutEntity, $settingsData, 'test'))
        ->toThrow(MoniteApiException::class, 'Clinic must have a Monite entity before updating settings');
});

it('throws exception when clinic has no monite entity id for update settings with different data', function () {
    $clinicWithoutEntity = Clinic::factory()->create([
        'monite_entity_id' => null,
    ]);

    // Enable Monite feature for the clinic
    app(MoniteFeatureFlagService::class)->enable($clinicWithoutEntity);

    $settingsData = ['payable' => ['allow_cancel_duplicates_automatically' => false]];

    expect(fn () => $this->settingsService->updateSettings($clinicWithoutEntity, $settingsData, 'test'))
        ->toThrow(MoniteApiException::class, 'Clinic must have a Monite entity before updating settings');
});

it('throws exception when clinic has no monite entity id for get settings', function () {
    $clinicWithoutEntity = Clinic::factory()->create([
        'monite_entity_id' => null,
    ]);

    // Enable Monite feature for the clinic
    app(MoniteFeatureFlagService::class)->enable($clinicWithoutEntity);

    expect(fn () => $this->settingsService->getSettings($clinicWithoutEntity))
        ->toThrow(MoniteApiException::class, 'Clinic must have a Monite entity before retrieving settings');
});

it('throws exception when API request fails for update settings', function () {
    $response = mock(Response::class);
    $response->shouldReceive('successful')->andReturn(false);
    $response->shouldReceive('status')->andReturn(400);
    $response->shouldReceive('body')->andReturn('Bad Request');
    $response->shouldReceive('json')->andReturn(['error' => 'Invalid request']);

    $this->moniteClient
        ->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $settingsData = ['payable' => ['allow_cancel_duplicates_automatically' => true]];

    $this->moniteClient
        ->shouldReceive('patch')
        ->with('/settings', $settingsData)
        ->andReturn($response);

    expect(fn () => $this->settingsService->updateSettings($this->clinic, $settingsData, 'test'))
        ->toThrow(MoniteApiException::class);
});

it('throws exception when API request fails for update settings with different data', function () {
    $response = mock(Response::class);
    $response->shouldReceive('successful')->andReturn(false);
    $response->shouldReceive('status')->andReturn(400);
    $response->shouldReceive('body')->andReturn('Bad Request');
    $response->shouldReceive('json')->andReturn(['error' => 'Invalid request']);

    $this->moniteClient
        ->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $settingsData = ['payable' => ['allow_cancel_duplicates_automatically' => false]];

    $this->moniteClient
        ->shouldReceive('patch')
        ->with('/settings', $settingsData)
        ->andReturn($response);

    expect(fn () => $this->settingsService->updateSettings($this->clinic, $settingsData, 'test'))
        ->toThrow(MoniteApiException::class);
});

it('throws exception when API request fails for get settings', function () {
    $response = mock(Response::class);
    $response->shouldReceive('successful')->andReturn(false);
    $response->shouldReceive('status')->andReturn(400);
    $response->shouldReceive('body')->andReturn('Bad Request');
    $response->shouldReceive('json')->andReturn(['error' => 'Invalid request']);

    $this->moniteClient
        ->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $this->moniteClient
        ->shouldReceive('get')
        ->with('/settings')
        ->andReturn($response);

    expect(fn () => $this->settingsService->getSettings($this->clinic))
        ->toThrow(MoniteApiException::class);
});

it('uses configuration value for settings', function () {
    config(['monite.entity_settings.payable.allow_cancel_duplicates_automatically' => false]);

    $response = mock(Response::class);
    $response->shouldReceive('successful')->andReturn(true);

    $this->moniteClient
        ->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $settingsData = ['payable' => ['allow_cancel_duplicates_automatically' => false]];

    $this->moniteClient
        ->shouldReceive('patch')
        ->with('/settings', $settingsData)
        ->andReturn($response);

    $this->settingsService->updateSettings($this->clinic, $settingsData, 'test operation');

    // If we get here without exception, the test passes
    expect(true)->toBeTrue();
});
