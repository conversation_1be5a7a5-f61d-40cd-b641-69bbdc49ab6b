<?php

declare(strict_types=1);

use App\Modules\Gpo\Models\GpoUser;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Password;
use Illuminate\Testing\TestResponse;

function requestGpoPasswordReset(string $email): TestResponse
{
    return test()->postJson('/api/gpo/password-resets', compact('email'));
}

function resetGpoPassword(string $token, string $email, string $password): TestResponse
{
    return test()->patchJson('/api/gpo/users/me/password', compact('token', 'email', 'password'));
}

beforeEach(function () {
    $this->gpoUser = GpoUser::factory()->create(['email' => '<EMAIL>']);
});

describe('GPO password reset', function () {
    describe('requesting password reset', function () {
        it('successfully handles valid password reset request', function () {
            requestGpoPasswordReset($this->gpoUser->email)
                ->assertNoContent();

            $this->assertDatabaseHas('gpo_password_reset_tokens', ['email' => $this->gpoUser->email]);
        });

        it('handles password reset request with the user being authenticated', function () {
            $this->actingAs($this->gpoUser, 'gpo');

            requestGpoPasswordReset($this->gpoUser->email)
                ->assertNoContent();
        });

        it('handles invalid email for password reset request', function () {
            requestGpoPasswordReset('<EMAIL>')
                ->assertNoContent();
        });

        it('prevents duplicate password reset requests', function () {
            requestGpoPasswordReset($this->gpoUser->email);

            requestGpoPasswordReset($this->gpoUser->email)
                ->assertUnprocessable()
                ->assertJsonValidationErrors('email');
        });

        it('sends a notification to the GPO user', function () {
            Notification::fake();

            requestGpoPasswordReset($this->gpoUser->email);

            Notification::assertSentTo($this->gpoUser, ResetPassword::class);
        });
    });

    describe('resetting password', function () {
        it('successfully resets password with valid token', function () {
            $token = Password::broker('gpo_users')->createToken($this->gpoUser);
            $newPassword = 'new-secure-password-123';

            resetGpoPassword($token, $this->gpoUser->email, $newPassword)
                ->assertSuccessful()
                ->assertJsonStructure([
                    'id',
                    'name',
                    'email',
                    'account' => [
                        'id',
                        'name',
                    ],
                ]);

            $this->gpoUser->refresh();
            $this->assertTrue(Hash::check($newPassword, $this->gpoUser->password));
        });

        it('fails with invalid token', function () {
            resetGpoPassword('invalid-token', $this->gpoUser->email, 'new-password-123')
                ->assertUnprocessable()
                ->assertJsonValidationErrors('email');
        });

        it('fails with invalid email', function () {
            $token = Password::broker('gpo_users')->createToken($this->gpoUser);

            resetGpoPassword($token, '<EMAIL>', 'new-password-123')
                ->assertUnprocessable()
                ->assertJsonValidationErrors('email');
        });

        it('fails with weak password', function () {
            $token = Password::broker('gpo_users')->createToken($this->gpoUser);

            resetGpoPassword($token, $this->gpoUser->email, '123')
                ->assertUnprocessable()
                ->assertJsonValidationErrors('password');
        });

        it('fails with expired token', function () {
            $token = Password::broker('gpo_users')->createToken($this->gpoUser);

            // Simulate token expiration by traveling in time
            $this->travel(61)->minutes();

            resetGpoPassword($token, $this->gpoUser->email, 'new-password-123')
                ->assertUnprocessable()
                ->assertJsonValidationErrors('email');
        });

        it('removes token after successful password reset', function () {
            $token = Password::broker('gpo_users')->createToken($this->gpoUser);

            resetGpoPassword($token, $this->gpoUser->email, 'new-password-123')
                ->assertSuccessful();

            $this->assertDatabaseMissing('gpo_password_reset_tokens', ['email' => $this->gpoUser->email]);
        });

        it('fires password reset event', function () {
            Event::fake();
            $token = Password::broker('gpo_users')->createToken($this->gpoUser);

            resetGpoPassword($token, $this->gpoUser->email, 'new-password-123');

            Event::assertDispatched(PasswordReset::class, function ($event) {
                return $event->user->id === $this->gpoUser->id;
            });
        });
    });
});
