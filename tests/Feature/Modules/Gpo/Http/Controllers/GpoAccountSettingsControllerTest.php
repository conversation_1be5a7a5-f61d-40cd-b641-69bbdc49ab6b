<?php

declare(strict_types=1);

use App\Models\Vendor;
use App\Modules\Gpo\Enums\GpoSettingKey;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Models\GpoAccountSettings;
use App\Modules\Gpo\Models\GpoUser;

beforeEach(function () {
    $this->gpoAccount = GpoAccount::factory()->create();
    $this->gpoUser = GpoUser::factory()->create([
        'account_id' => $this->gpoAccount->id,
    ]);
    $this->token = $this->gpoUser->createToken('test-token')->plainTextToken;
    $this->vendor = Vendor::factory()->create();

    $this->setting1 = GpoAccountSettings::factory()
        ->settingKey(GpoSettingKey::PerformanceThreshold)
        ->create([
            'gpo_account_id' => $this->gpoAccount->id,
        ]);

    $this->setting2 = GpoAccountSettings::factory()
        ->settingKey(GpoSettingKey::VendorGoals)
        ->create([
            'gpo_account_id' => $this->gpoAccount->id,
        ]);
});

describe('Index - List all settings', function () {
    it('returns all settings for authenticated GPO account', function () {
        // Create a setting for another account (should not be returned)
        $otherAccount = GpoAccount::factory()->create();
        GpoAccountSettings::factory()
            ->settingKey(GpoSettingKey::PerformanceThreshold)
            ->create([
                'gpo_account_id' => $otherAccount->id,
            ]);

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->getJson('/api/gpo/account-settings');

        $response->assertOk()
            ->assertJsonCount(2)
            ->assertJsonFragment([
                'id' => $this->setting1->id,
                'setting_key' => $this->setting1->setting_key,
            ])
            ->assertJsonFragment([
                'id' => $this->setting2->id,
                'setting_key' => $this->setting2->setting_key,
            ]);
    });

    it('returns empty array when no settings exist', function () {
        $this->gpoAccount->settings()->delete();

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->getJson('/api/gpo/account-settings');

        $response->assertOk()
            ->assertJsonCount(0);
    });

    it('requires authentication', function () {
        $response = $this->getJson('/api/gpo/account-settings');

        $response->assertUnauthorized()
            ->assertJson([
                'message' => 'Unauthenticated.',
            ]);
    });
});

describe('Store - Create new setting', function () {
    it('creates a performance threshold setting', function () {
        $this->gpoAccount->settings()->delete();

        $payload = [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 85,
                'notification_message' => 'Performance threshold alert',
            ],
        ];

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->postJson('/api/gpo/account-settings', $payload);

        $response->assertCreated()
            ->assertJsonFragment([
                'gpo_account_id' => $this->gpoAccount->id,
                'setting_key' => GpoSettingKey::PerformanceThreshold->value,
                'value' => [
                    'enabled' => true,
                    'threshold' => 85,
                    'notification_message' => 'Performance threshold alert',
                ],
            ]);

        $this->assertDatabaseHas('gpo_account_settings', [
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
        ]);
    });

    it('creates a vendor goals setting', function () {
        $this->gpoAccount->settings()->delete();

        $payload = [
            'setting_key' => GpoSettingKey::VendorGoals->value,
            'value' => [
                'goals' => [
                    [
                        'enabled' => true,
                        'goal_amount' => 1000000,
                        'vendor_id' => $this->vendor->id,
                    ],
                ],
            ],
        ];

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->postJson('/api/gpo/account-settings', $payload);

        $response->assertCreated()
            ->assertJsonFragment([
                'gpo_account_id' => $this->gpoAccount->id,
                'setting_key' => GpoSettingKey::VendorGoals->value,
            ]);
    });

    it('requires setting_key field', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->postJson('/api/gpo/account-settings', [
            'value' => ['enabled' => true],
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['setting_key']);
    });

    it('requires value field', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->postJson('/api/gpo/account-settings', [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['value']);
    });

    it('validates value must be an array', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->postJson('/api/gpo/account-settings', [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => 'not an array',
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['value']);
    });

    it('validates setting_key must be valid enum value', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->postJson('/api/gpo/account-settings', [
            'setting_key' => 'invalid_setting_key',
            'value' => ['enabled' => true],
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['setting_key']);
    });

    it('prevents duplicate setting_key for same account', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->postJson('/api/gpo/account-settings', [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 85,
                'notification_message' => 'Performance threshold alert',
            ],
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['setting_key']);
    });

    it('requires authentication', function () {
        $response = $this->postJson('/api/gpo/account-settings', [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => ['enabled' => true],
        ]);

        $response->assertUnauthorized();
    });
});

describe('Show - Get single setting', function () {
    it('returns a single setting', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->getJson("/api/gpo/account-settings/{$this->setting1->id}");

        $response->assertOk()
            ->assertJsonFragment([
                'id' => $this->setting1->id,
                'gpo_account_id' => $this->gpoAccount->id,
                'setting_key' => $this->setting1->setting_key,
                'value' => $this->setting1->value,
            ]);
    });

    it('returns 404 for non-existent setting', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->getJson('/api/gpo/account-settings/'.fake()->uuid());

        $response->assertNotFound();
    });

    it('returns 403 when accessing another account setting', function () {
        $otherAccount = GpoAccount::factory()->create();
        $otherSetting = GpoAccountSettings::factory()
            ->settingKey(GpoSettingKey::PerformanceThreshold)
            ->create([
                'gpo_account_id' => $otherAccount->id,
            ]);

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->getJson("/api/gpo/account-settings/{$otherSetting->id}");

        $response->assertForbidden()
            ->assertJson([
                'message' => 'You do not have permission to view this setting.',
            ]);
    });

    it('requires authentication', function () {
        $response = $this->getJson("/api/gpo/account-settings/{$this->setting1->id}");

        $response->assertUnauthorized();
    });
});

describe('Update - Modify setting', function () {
    it('updates setting value', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->patchJson("/api/gpo/account-settings/{$this->setting1->id}", [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 90,
                'notification_message' => 'Updated message',
            ],
        ]);

        $response->assertOk()
            ->assertJsonFragment([
                'id' => $this->setting1->id,
                'value' => [
                    'enabled' => true,
                    'threshold' => 90,
                    'notification_message' => 'Updated message',
                ],
            ]);

        $this->assertDatabaseHas('gpo_account_settings', [
            'id' => $this->setting1->id,
            'gpo_account_id' => $this->gpoAccount->id,
        ]);
    });

    it('updates setting key', function () {
        $this->setting2->delete();

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->patchJson("/api/gpo/account-settings/{$this->setting1->id}", [
            'setting_key' => GpoSettingKey::VendorGoals->value,
            'value' => [
                'goals' => [
                    [
                        'enabled' => true,
                        'goal_amount' => 1000000,
                        'vendor_id' => $this->vendor->id,
                    ],
                ],
            ],
        ]);

        $response->assertOk()
            ->assertJsonFragment([
                'setting_key' => GpoSettingKey::VendorGoals->value,
            ]);

        $this->assertDatabaseHas('gpo_account_settings', [
            'id' => $this->setting1->id,
            'gpo_account_id' => $this->gpoAccount->id,
            'setting_key' => GpoSettingKey::VendorGoals->value,
        ]);
    });

    it('does not allow duplicate setting key', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->patchJson("/api/gpo/account-settings/{$this->setting1->id}", [
            'setting_key' => GpoSettingKey::VendorGoals->value,
            'value' => [
                'goals' => [
                    [
                        'enabled' => true,
                        'goal_amount' => 1000000,
                        'vendor_id' => $this->vendor->id,
                    ],
                ],
            ],
        ]);

        $response->assertUnprocessable()
            ->assertJson([
                'message' => 'Setting key must be unique.',
            ]);

        expect($this->setting1->fresh()->setting_key)->toBe(GpoSettingKey::PerformanceThreshold);
    });

    it('does not allow partial updates', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->patchJson("/api/gpo/account-settings/{$this->setting1->id}", [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
        ]);

        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['value']);

        expect($this->setting1->fresh()->setting_key)->toBe(GpoSettingKey::PerformanceThreshold);
    });

    it('returns 403 when updating another account setting', function () {
        $otherAccount = GpoAccount::factory()->create();
        $otherSetting = GpoAccountSettings::factory()
            ->settingKey(GpoSettingKey::PerformanceThreshold)
            ->create([
                'gpo_account_id' => $otherAccount->id,
            ]);

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->patchJson("/api/gpo/account-settings/{$otherSetting->id}", [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 90,
                'notification_message' => 'Updated message',
            ],
        ]);

        $response->assertForbidden()
            ->assertJson([
                'message' => 'You do not have permission to update this setting.',
            ]);
    });

    it('returns 404 for non-existent setting', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->patchJson('/api/gpo/account-settings/'.fake()->uuid(), [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 90,
                'notification_message' => 'Updated message',
            ],
        ]);

        $response->assertNotFound();
    });

    it('requires authentication', function () {
        $response = $this->patchJson("/api/gpo/account-settings/{$this->setting1->id}", [
            'setting_key' => GpoSettingKey::PerformanceThreshold->value,
            'value' => [
                'enabled' => true,
                'threshold' => 90,
                'notification_message' => 'Updated message',
            ],
        ]);

        $response->assertUnauthorized();
    });
});

describe('Destroy - Delete setting', function () {
    it('deletes a setting', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->deleteJson("/api/gpo/account-settings/{$this->setting1->id}");

        $response->assertNoContent();

        $this->assertDatabaseMissing('gpo_account_settings', [
            'id' => $this->setting1->id,
        ]);
    });

    it('returns 403 when deleting another account setting', function () {
        $otherAccount = GpoAccount::factory()->create();
        $otherSetting = GpoAccountSettings::factory()
            ->settingKey(GpoSettingKey::PerformanceThreshold)
            ->create([
                'gpo_account_id' => $otherAccount->id,
            ]);

        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->deleteJson("/api/gpo/account-settings/{$otherSetting->id}");

        $response->assertForbidden()
            ->assertJson([
                'message' => 'You do not have permission to delete this setting.',
            ]);

        $this->assertDatabaseHas('gpo_account_settings', [
            'id' => $otherSetting->id,
        ]);
    });

    it('returns 404 for non-existent setting', function () {
        $response = $this->withHeaders([
            'Authorization' => "Bearer {$this->token}",
        ])->deleteJson('/api/gpo/account-settings/'.fake()->uuid());

        $response->assertNotFound();
    });

    it('requires authentication', function () {
        $response = $this->deleteJson("/api/gpo/account-settings/{$this->setting1->id}");

        $response->assertUnauthorized();
    });
});
