<?php

declare(strict_types=1);

use App\Enums\ShippingType;
use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Models\VendorClinicShippingSetting;

beforeEach(function () {
    $this->clinic = Clinic::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->clinic->clinic_account_id,
    ]);
    $this->vendor = Vendor::factory()->create(['is_enabled' => true]);
    $this->user->clinics()->attach($this->clinic->id);

    $this->actingAs($this->user, 'sanctum');
});

test('can create a new shipping setting with flat fee', function () {
    $data = [
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'shipping_type' => ShippingType::FlatFee->value,
        'default_shipping_fee' => 15.00, // $15.00 in dollars (will be converted to 1500 cents)
    ];

    $response = $this->postJson("/api/clinics/{$this->clinic->id}/shipping-settings", $data);

    $response->assertCreated()
        ->assertJsonStructure([
            'id',
            'vendorId',
            'clinicId',
            'shippingType',
            'defaultShippingFee',
            'freeShippingThreshold',
            'cutoffTime',
            'createdAt',
            'updatedAt',
        ])
        ->assertJson([
            'vendorId' => $this->vendor->id,
            'clinicId' => $this->clinic->id,
            'shippingType' => ShippingType::FlatFee->value,
            'defaultShippingFee' => 15.00, // Should return as dollars
            'freeShippingThreshold' => null,
        ]);

    $this->assertDatabaseHas('vendor_clinic_shipping_settings', [
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'shipping_type' => ShippingType::FlatFee->value,
        'default_shipping_fee' => 1500, // $15.00 in cents
    ]);
});

test('can create a new shipping setting with always free', function () {
    $data = [
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'shipping_type' => ShippingType::AlwaysFree->value,
    ];

    $response = $this->postJson("/api/clinics/{$this->clinic->id}/shipping-settings", $data);

    $response->assertCreated()
        ->assertJson([
            'vendorId' => $this->vendor->id,
            'clinicId' => $this->clinic->id,
            'shippingType' => ShippingType::AlwaysFree->value,
            'defaultShippingFee' => null,
            'freeShippingThreshold' => null,
        ]);
});

test('validates required fields', function () {
    $response = $this->postJson("/api/clinics/{$this->clinic->id}/shipping-settings", []);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['vendorId', 'clinicId', 'shippingType']);
});

test('validates vendor exists and is enabled', function () {
    $disabledVendor = Vendor::factory()->create(['is_enabled' => false]);

    $data = [
        'vendor_id' => $disabledVendor->id,
        'clinic_id' => $this->clinic->id,
        'shipping_type' => ShippingType::FlatFee->value,
        'default_shipping_fee' => 15.00, // $15.00 in dollars (will be converted to 1500 cents)
    ];

    $response = $this->postJson("/api/clinics/{$this->clinic->id}/shipping-settings", $data);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['vendorId']);
});

test('can update a shipping setting', function () {
    $setting = VendorClinicShippingSetting::factory()->flatFee(1500)->create([
        'clinic_id' => $this->clinic->id,
        'vendor_id' => $this->vendor->id,
    ]);

    $data = [
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'shipping_type' => ShippingType::FreeOverMinimum->value,
        'default_shipping_fee' => 20.00, // $20.00 in dollars (will be converted to 2000 cents)
        'free_shipping_threshold' => 150.00, // $150.00 in dollars (will be converted to 15000 cents)
    ];

    $response = $this->postJson("/api/clinics/{$this->clinic->id}/shipping-settings", $data);

    $response->assertOk();

    $setting->refresh();
    expect($setting->shipping_type)->toBe(ShippingType::FreeOverMinimum);
    expect($setting->default_shipping_fee)->toBe(2000); // $20.00 in cents
    expect($setting->free_shipping_threshold)->toBe(15000); // $150.00 in cents
});

test('can delete a shipping setting', function () {
    $setting = VendorClinicShippingSetting::factory()->create([
        'clinic_id' => $this->clinic->id,
    ]);

    $response = $this->deleteJson("/api/clinics/{$this->clinic->id}/shipping-settings/{$setting->id}");

    $response->assertNoContent();

    $this->assertDatabaseMissing('vendor_clinic_shipping_settings', [
        'id' => $setting->id,
    ]);
});
