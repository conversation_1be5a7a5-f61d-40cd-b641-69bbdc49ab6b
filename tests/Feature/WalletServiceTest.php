<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ClinicWallet;
use App\Models\WalletDefinition;
use App\Models\WalletTransaction;
use App\Services\WalletService;

test('wallet service can get or create wallet', function () {
    $clinic = Clinic::factory()->create();
    $walletDefinition = WalletDefinition::factory()->create();
    $walletService = new WalletService();

    $wallet = $walletService->getOrCreateWallet($clinic, $walletDefinition);

    expect($wallet)->toBeInstanceOf(ClinicWallet::class);
    expect($wallet->clinic_id)->toBe($clinic->id);
    expect($wallet->wallet_definition_id)->toBe($walletDefinition->id);
    expect($wallet->balance)->toBe(0);
});

test('wallet service returns existing wallet when already exists', function () {
    $clinic = Clinic::factory()->create();
    $walletDefinition = WalletDefinition::factory()->create();
    $existingWallet = ClinicWallet::factory()->create([
        'clinic_id' => $clinic->id,
        'wallet_definition_id' => $walletDefinition->id,
        'balance' => 1000,
    ]);
    $walletService = new WalletService();

    $wallet = $walletService->getOrCreateWallet($clinic, $walletDefinition);

    expect($wallet->id)->toBe($existingWallet->id);
    expect($wallet->balance)->toBe(1000);
});

test('wallet service can record credit transaction', function () {
    $wallet = ClinicWallet::factory()->zeroBalance()->create();
    $walletService = new WalletService();

    $transaction = $walletService->creditWallet(
        $wallet,
        1000, // $10.00
        'Test credit transaction'
    );

    expect($transaction)->toBeInstanceOf(WalletTransaction::class);
    expect($transaction->type)->toBe('credit');
    expect($transaction->amount)->toBe(1000);
    expect($transaction->status)->toBe('approved');
    expect($wallet->fresh()->balance)->toBe(1000);
});

test('wallet service can record debit transaction', function () {
    $wallet = ClinicWallet::factory()->withBalance(2000)->create(); // $20.00
    $walletService = new WalletService();

    $transaction = $walletService->debitWallet(
        $wallet,
        500, // $5.00
        'Test debit transaction'
    );

    expect($transaction)->toBeInstanceOf(WalletTransaction::class);
    expect($transaction->type)->toBe('debit');
    expect($transaction->amount)->toBe(500);
    expect($transaction->status)->toBe('approved');
    expect($wallet->fresh()->balance)->toBe(1500); // $20.00 - $5.00 = $15.00
});

test('wallet service can record transaction with source and metadata', function () {
    $wallet = ClinicWallet::factory()->zeroBalance()->create();
    $walletService = new WalletService();
    $sourceId = (string) Illuminate\Support\Str::uuid();

    $transaction = $walletService->recordTransaction(
        $wallet,
        1000,
        'credit',
        'Order cashback',
        'Order',
        $sourceId,
        ['order_number' => 'ORD-001']
    );

    expect($transaction->description)->toBe('Order cashback');
    expect($transaction->source_type)->toBe('Order');
    expect($transaction->source_id)->toBe($sourceId);
    expect($transaction->metadata)->toBe(['order_number' => 'ORD-001']);
});

test('wallet service can record pending transaction without updating balance', function () {
    $wallet = ClinicWallet::factory()->zeroBalance()->create();
    $walletService = new WalletService();

    $transaction = $walletService->recordTransaction(
        $wallet,
        1000,
        'credit',
        'Pending credit',
        null,
        null,
        null,
        'pending'
    );

    expect($transaction->status)->toBe('pending');
    expect($wallet->fresh()->balance)->toBe(0); // Balance should not change for pending
});

test('wallet service can get transaction history', function () {
    $wallet = ClinicWallet::factory()->create();
    $walletService = new WalletService();

    // Create some transactions
    $walletService->creditWallet($wallet, 1000, 'Credit 1');
    $walletService->debitWallet($wallet, 300, 'Debit 1');
    $walletService->creditWallet($wallet, 500, 'Credit 2');

    $history = $walletService->getTransactionHistory($wallet);

    expect($history)->toHaveCount(3);
    expect($history->first()->description)->toBe('Credit 2'); // Most recent first
});

test('wallet service can get transaction history with limit', function () {
    $wallet = ClinicWallet::factory()->create();
    $walletService = new WalletService();

    // Create 5 transactions
    for ($i = 1; $i <= 5; $i++) {
        $walletService->creditWallet($wallet, 100, "Credit {$i}");
    }

    $history = $walletService->getTransactionHistory($wallet, 3);

    expect($history)->toHaveCount(3);
});

test('wallet service can get clinic wallets', function () {
    $clinic = Clinic::factory()->create();
    $walletDefinition1 = WalletDefinition::factory()->create();
    $walletDefinition2 = WalletDefinition::factory()->create();

    $wallet1 = ClinicWallet::factory()->create([
        'clinic_id' => $clinic->id,
        'wallet_definition_id' => $walletDefinition1->id,
    ]);

    $wallet2 = ClinicWallet::factory()->create([
        'clinic_id' => $clinic->id,
        'wallet_definition_id' => $walletDefinition2->id,
    ]);

    $walletService = new WalletService();
    $wallets = $walletService->getClinicWallets($clinic);

    expect($wallets)->toHaveCount(2);
    expect($wallets->pluck('id')->toArray())->toContain($wallet1->id, $wallet2->id);
});

test('wallet service can get wallet balance in dollars', function () {
    $wallet = ClinicWallet::factory()->withBalance(1500)->create(); // $15.00
    $walletService = new WalletService();

    $balanceInDollars = $walletService->getWalletBalanceInDollars($wallet);

    expect($balanceInDollars)->toBe(15.0);
});

test('wallet service handles negative debit amounts correctly', function () {
    $wallet = ClinicWallet::factory()->withBalance(1000)->create();
    $walletService = new WalletService();

    $transaction = $walletService->debitWallet(
        $wallet,
        -500, // Negative amount should be handled correctly
        'Test negative debit'
    );

    expect($transaction->amount)->toBe(500); // Should be converted to positive
    expect($wallet->fresh()->balance)->toBe(500); // $10.00 - $5.00 = $5.00
});
