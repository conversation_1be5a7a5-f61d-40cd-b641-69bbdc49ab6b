<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('clinic_wallet_id')->constrained()->onDelete('cascade');
            $table->bigInteger('amount')->comment('Amount in cents, positive for credits, negative for debits');
            $table->enum('type', ['credit', 'debit']);
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('approved');
            $table->string('description');
            $table->string('source_type')->nullable()->comment('Order, Promotion, etc.');
            $table->uuid('source_id')->nullable()->comment('ID of the source record');
            $table->json('metadata')->nullable()->comment('Additional transaction data');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['clinic_wallet_id', 'created_at']);
            $table->index(['source_type', 'source_id']);
            $table->index(['status', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
