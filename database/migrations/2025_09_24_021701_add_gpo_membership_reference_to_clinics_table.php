<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clinics', function (Blueprint $table) {
            $table->string('gpo_membership_reference')->nullable()->after('gpo_account_id');
            $table->date('gpo_membership_since')->nullable()->after('gpo_membership_reference');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinics', function (Blueprint $table) {
            $table->dropColumn('gpo_membership_reference');
            $table->dropColumn('gpo_membership_since');
        });
    }
};
