<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinic_wallets', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('clinic_id')->constrained()->onDelete('cascade');
            $table->foreignUuid('wallet_definition_id')->constrained()->onDelete('cascade');
            $table->bigInteger('balance')->default(0)->comment('Balance in cents');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['clinic_id', 'wallet_definition_id']);
            $table->index(['clinic_id', 'balance']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinic_wallets');
    }
};
