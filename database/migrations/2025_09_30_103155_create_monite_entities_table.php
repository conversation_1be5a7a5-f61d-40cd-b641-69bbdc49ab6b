<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monite_entities', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('monite_entity_id')->unique()->index();
            $table->foreignUuid('clinic_id')->constrained('clinics')->cascadeOnDelete();
            $table->jsonb('entity_data');
            $table->jsonb('onboarding_requirements')->nullable();
            $table->string('status')->nullable()->index();
            $table->string('type')->nullable();
            $table->string('email')->nullable();
            $table->timestamp('monite_created_at')->nullable();
            $table->timestamp('monite_updated_at')->nullable();
            $table->timestamps();

            $table->index('clinic_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monite_entities');
    }
};
