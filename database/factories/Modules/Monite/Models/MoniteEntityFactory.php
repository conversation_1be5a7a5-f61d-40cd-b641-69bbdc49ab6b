<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Monite\Models;

use App\Models\Clinic;
use App\Modules\Monite\Models\MoniteEntity;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\Monite\Models\MoniteEntity>
 */
class MoniteEntityFactory extends Factory
{
    protected $model = MoniteEntity::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $moniteEntityId = $this->faker->uuid();
        $createdAt = $this->faker->dateTimeBetween('-1 year', 'now');
        $updatedAt = $this->faker->dateTimeBetween($createdAt, 'now');

        return [
            'monite_entity_id' => $moniteEntityId,
            'clinic_id' => Clinic::factory(),
            'entity_data' => [
                'id' => $moniteEntityId,
                'created_at' => $createdAt->format('Y-m-d\TH:i:s.u\Z'),
                'updated_at' => $updatedAt->format('Y-m-d\TH:i:s.u\Z'),
                'address' => [
                    'country' => 'US',
                    'city' => $this->faker->city(),
                    'postal_code' => $this->faker->postcode(),
                    'state' => $this->faker->stateAbbr(),
                    'line1' => $this->faker->streetAddress(),
                    'line2' => null,
                ],
                'email' => $this->faker->companyEmail(),
                'logo' => null,
                'organization' => [
                    'legal_name' => $this->faker->company(),
                    'legal_entity_id' => null,
                    'directors_provided' => true,
                    'executives_provided' => false,
                    'owners_provided' => true,
                    'representative_provided' => true,
                    'business_structure' => null,
                ],
                'phone' => $this->faker->phoneNumber(),
                'registration_authority' => null,
                'registration_number' => null,
                'status' => 'active',
                'tax_id' => $this->faker->numerify('##-#######'),
                'type' => 'organization',
                'website' => null,
            ],
            'onboarding_requirements' => [
                'payment_method' => 'card',
                'requirements' => [
                    'currently_due' => [],
                    'eventually_due' => [],
                    'past_due' => [],
                    'pending_verification' => [],
                    'current_deadline' => null,
                ],
                'requirements_errors' => [],
                'verification_errors' => [],
                'verification_status' => 'enabled',
                'disabled_reason' => null,
            ],
            'status' => 'active',
            'type' => 'organization',
            'email' => $this->faker->companyEmail(),
            'mailbox_address' => preg_replace('/[^a-z0-9-]/', '', mb_strtolower(str_replace(' ', '-', $this->faker->company()))).'-<EMAIL>',
            'monite_created_at' => $createdAt,
            'monite_updated_at' => $updatedAt,
        ];
    }

    /**
     * Indicate that the entity is pending onboarding
     */
    public function pending(): static
    {
        return $this->state(function (array $attributes) {
            $entityData = $attributes['entity_data'];
            $entityData['status'] = 'pending';

            return [
                'entity_data' => $entityData,
                'onboarding_requirements' => [
                    'payment_method' => 'card',
                    'requirements' => [
                        'currently_due' => ['business_profile.url', 'tos_acceptance.date'],
                        'eventually_due' => [],
                        'past_due' => [],
                        'pending_verification' => [],
                        'current_deadline' => now()->addDays(7)->format('Y-m-d\TH:i:s\Z'),
                    ],
                    'requirements_errors' => [],
                    'verification_errors' => [],
                    'verification_status' => 'pending',
                    'disabled_reason' => null,
                ],
                'status' => 'pending',
            ];
        });
    }

    /**
     * Indicate that the entity is active (onboarding complete)
     */
    public function active(): static
    {
        return $this->state(function (array $attributes) {
            $entityData = $attributes['entity_data'];
            $entityData['status'] = 'active';

            return [
                'entity_data' => $entityData,
                'status' => 'active',
            ];
        });
    }
}
