<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\ClinicWallet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WalletTransaction>
 */
class WalletTransactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['credit', 'debit']);
        $amount = $this->faker->numberBetween(100, 5000); // $1.00 to $50.00 in cents

        return [
            'clinic_wallet_id' => ClinicWallet::factory(),
            'amount' => $type === 'debit' ? -$amount : $amount,
            'type' => $type,
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
            'description' => $this->faker->sentence(),
            'source_type' => $this->faker->optional(0.7)->randomElement(['Order', 'Promotion', 'Manual']),
            'source_id' => $this->faker->optional(0.7)->uuid(),
            'metadata' => $this->faker->optional(0.3)->randomElements([
                'order_number' => $this->faker->numerify('ORD-####'),
                'promotion_code' => $this->faker->lexify('PROMO-???'),
                'admin_note' => $this->faker->sentence(),
            ]),
        ];
    }

    /**
     * Indicate that the transaction is a credit.
     */
    public function credit(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'credit',
            'amount' => abs($attributes['amount'] ?? $this->faker->numberBetween(100, 5000)),
        ]);
    }

    /**
     * Indicate that the transaction is a debit.
     */
    public function debit(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'debit',
            'amount' => -abs($attributes['amount'] ?? $this->faker->numberBetween(100, 5000)),
        ]);
    }

    /**
     * Indicate that the transaction is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
        ]);
    }

    /**
     * Indicate that the transaction is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the transaction is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
        ]);
    }
}
