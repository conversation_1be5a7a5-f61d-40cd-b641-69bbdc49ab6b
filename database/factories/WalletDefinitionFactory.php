<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WalletDefinition>
 */
class WalletDefinitionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'vendor_id' => Vendor::factory(),
            'name' => $this->faker->words(3, true).' Wallet',
            'description' => $this->faker->optional(0.7)->sentence(10),
            'settings' => [
                'rules' => [
                    'minimum_purchase_amount' => $this->faker->numberBetween(100, 1000),
                    'cashback_percentage' => $this->faker->numberBetween(1, 10),
                    'maximum_cashback' => $this->faker->numberBetween(50, 500),
                ],
                'expiry' => [
                    'type' => $this->faker->randomElement(['days', 'months', 'years']),
                    'value' => $this->faker->numberBetween(30, 365),
                ],
                'redemption_logic' => [
                    'auto_apply' => $this->faker->boolean(),
                    'minimum_redemption_amount' => $this->faker->numberBetween(10, 100),
                    'redemption_cap' => $this->faker->numberBetween(100, 1000),
                ],
            ],
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Indicate that the wallet definition is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the wallet definition is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
